import nwbuild from "nw-builder";

await nwbuild({
  mode: "build",
  flavor: "normal",
  platform: "win",
  arch: "ia32",
  cacheDir: "./node_modules/nw",
  logLevel: "debug",
  srcDir: "./src",
  outDir: "./out",
  glob: false,
  version: "0.72.0",
  nodeAddon: "gyp",
  managedManifest: false,
  app: {
    name: "玉玄宫收银系统",
    icon: "./src/tabletop.ico",
    version: "1.0.0",
    company: "玉玄宫",
    fileDescription: "玉玄宫收银系统",
    fileVersion: "1.0.0",
    internalName: "玉玄宫收银系统",
    legalCopyright: "Copyright (c) 2024 玉玄宫",
    originalFilename: "玉玄宫收银系统",
    productName: "玉玄宫收银系统",
    productVersion: "1.0.0",
  },
});


