<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />

    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>设置</title>
    <link rel="stylesheet" href="./vue/element/<EMAIL>" />
    <link rel="stylesheet" href="./css/css-comment.css" />
    <link
      rel="stylesheet"
      href="https://at.alicdn.com/t/font_1156348_lrijkzmtfh.css"
    />
    <link rel="stylesheet" href="css/shezhi.css" />
  </head>
  <body>
    <div id="app" v-cloak>
      <!--顶部导航条-->
      <div>
        <el-row>
          <el-col :xs="20" :sm="20" :md="20" :lg="20" :xl="20">
            <div class="menu">
              <div class="menu_font">
                <div
                  class="menu_font_nav"
                  v-for="(value,key) in todos1"
                  @click="getIndex(key)"
                  :class="isactive == key ? 'addclass' : '' "
                >
                  {{value.word}}
                </div>
              </div>
            </div>
          </el-col>
          <el-col :xs="4" :sm="4" :md="4" :lg="4" :xl="4">
            <div class="cashier">
              <div class="li1">
                <img
                  class="li1_img"
                  src="./images/shouhuoyauntouxiang.jpg"
                  alt=""
                />
              </div>
              <div class="li2">{{cashier}}</div>
            </div>
          </el-col>
        </el-row>
      </div>
      <!--主体-->
      <div class="main">
        <!--左侧导航条-->
        <ul class="main-right">
          <li style="display: flex; width: 100%">
            <!--选择订单内容-->
            <div class="server">
              <div class="open_details_border">
                <div class="open_details_title">设置</div>
                <div>
                  <div
                    class="sz_title1_info1"
                    @click="sz_title1_info1_click"
                    :class="title1_num == 1 ? 'title1_add_class':'' "
                  >
                    <div class="sz_title1_info1_font1">预约提示音</div>
                    <div class="sz_title1_info1_font2">
                      {{sz_title1_yuyue_open}}
                    </div>
                  </div>
                  <div
                    class="sz_title1_info2"
                    @click="sz_title1_info2_click"
                    :class="title1_num == 2 ? 'title1_add_class':'' "
                  >
                    小票打印机
                  </div>
                  <div
                    class="sz_title1_info3"
                    @click="sz_title1_info3_click"
                    :class="title1_num == 3 ? 'title1_add_class':'' "
                  >
                    <div class="sz_title1_info3_font1">抹零</div>
                    <div class="sz_title1_info3_font2">
                      {{sz_title1_molin_open}}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!--开单内容-->
            <div class="open_details">
              <!--开单详情标题-->
              <div class="open_details_border">
                <div class="open_details_title">{{shezhi_title2}}</div>
                <div v-if="title2_num == 1">
                  <div class="sz_title2_info1">
                    <div class="sz_title2_info1_font1">预约提示声音提醒</div>
                    <div class="sz_title2_info1_font2">
                      <el-switch
                        v-model="yuyue_tishi_value"
                        @change="yuyue_open"
                        active-color="#3e63dd"
                        inactive-color="#DDDDDD"
                      ></el-switch>
                    </div>
                  </div>
                  <div class="sz_title2_info2">
                    No open, no prompt for appointment. After opening, the
                    appointment has the prompt sound prompt.
                  </div>
                  <div class="sz_title2_info3">
                    <div class="sz_title2_info3_font1">夜间免打扰</div>
                    <div class="sz_title2_info3_font2">
                      <el-switch
                        v-model="yejian_miandarao_value"
                        @change="yejian_miandarao"
                        active-color="#3e63dd"
                        inactive-color="#DDDDDD"
                      ></el-switch>
                    </div>
                  </div>
                  <div class="sz_title2_info4">
                    No Disturbing Information at Night
                  </div>
                </div>
                <div v-if="title2_num == 2">
                  <div class="sz_title2_info5">
                    <div class="sz_title2_info5_font1">SunMi</div>
                    <div class="sz_title2_info5_font2">
                      <div
                        class="sz_title2_info5_font2_1"
                        v-if="dayin1"
                        @click="dayin1_click"
                      >
                        打印测试
                      </div>
                      <div
                        class="sz_title2_info5_font2_2"
                        :class="dayin1_duan ? 'dayin1_duan_add':'' "
                        @click="dayin1_duan_click"
                      >
                        断开
                      </div>
                    </div>
                  </div>
                  <div class="sz_title2_info5">
                    <div class="sz_title2_info5_font1">USB</div>
                    <div class="sz_title2_info5_font2">
                      <div
                        class="sz_title2_info5_font2_1"
                        v-if="dayin2"
                        @click="dayin2_click"
                      >
                        打印测试
                      </div>
                      <div
                        class="sz_title2_info5_font2_2"
                        :class="dayin2_duan ? 'dayin2_duan_add':'' "
                        @click="dayin2_duan_click"
                      >
                        断开
                      </div>
                    </div>
                  </div>
                  <div class="sz_title2_info6">
                    Click to change the printer name, and press to remove the
                    printer
                  </div>
                </div>
                <div v-if="title2_num == 3">
                  <div class="sz_title2_info7">
                    <div class="sz_title2_info7_font1">
                      收款时合计金额抹除角分金额
                    </div>
                    <div class="sz_title2_info7_font2">
                      <el-switch
                        v-model="sz_molin_value"
                        @change="molin_open"
                        active-color="#3e63dd"
                        inactive-color="#DDDDDD"
                      ></el-switch>
                    </div>
                  </div>
                  <div class="sz_title2_info8">
                    开启后，如收款合计金额有角分金额，将自动抹零
                  </div>
                  <div class="sz_title2_info9">实例预览</div>
                  <div class="sz_title2_info10">
                    <div class="sz_title2_info10_font1">合计</div>
                    <div class="sz_title2_info10_font2">￥{{sz_hj_money}}</div>
                  </div>
                  <div class="sz_title2_info10" v-if="is_show_molin">
                    <div class="sz_title2_info10_font1">抹零</div>
                    <div class="sz_title2_info10_font2">￥{{sz_ml_money}}</div>
                  </div>
                  <div class="sz_title2_info10">
                    <div class="sz_title2_info10_font1">应收金额</div>
                    <div class="sz_title2_info10_font2">￥{{sz_ys_money}}</div>
                  </div>
                </div>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>

    <script src="./vue/vue2.5.16.js"></script>
    <script src="./vue/element/<EMAIL>"></script>
    <script src="js/plugin/<EMAIL>"></script>
  </body>

  <!--<script src="../node_modules/swiper/dist/js/swiper.min.js"></script>-->

  <script>
    // import { swiper, swiperSlide } from 'vue-awesome-swiper'
    var app = new Vue({
      el: "#app",
      data: function () {
        return {
          todos1: [
            { word: "收银台" },
            { word: "商品" },
            { word: "订单" },
            { word: "会员" },
            { word: "预约" },
            { word: "通知" },
            { word: "设置" },
          ],
          shezhi_title2: "预约提示音",
          isactive: 0,
          cashier: "售货员:小丑",
          title1_num: 1,
          title2_num: 1,
          yuyue_tishi_value: false,
          yejian_miandarao_value: false,
          sz_title1_yuyue_open: "未开启",
          sz_title1_yejian_open: "未开启",
          dayin1: true,
          dayin2: true,
          dayin1_duan: false,
          dayin2_duan: false,
          sz_title1_molin_open: "未开启",
          sz_molin_value: false,
          sz_hj_money: 888.88,
          sz_ml_money: null,
          sz_ys_money: null,
          is_show_molin: false,
        };
      },
      mounted: function () {
        this.sz_ys_money = this.sz_hj_money;
      },

      methods: {
        getIndex: function (key) {
          this.isactive = key;
        },
        sz_title1_info1_click: function () {
          this.title1_num = 1;
          this.title2_num = 1;
          this.shezhi_title2 = "预约提示音";
        },
        sz_title1_info2_click: function () {
          this.title1_num = 2;
          this.title2_num = 2;
          this.shezhi_title2 = "小票打印机";
        },
        sz_title1_info3_click: function () {
          this.title1_num = 3;
          this.title2_num = 3;
          this.shezhi_title2 = "抹零";
        },
        yuyue_open: function () {
          if (this.yuyue_tishi_value) {
            this.sz_title1_yuyue_open = "开启";
          } else {
            this.sz_title1_yuyue_open = "未开启";
          }
        },
        molin_open: function () {
          this.is_show_molin = !this.is_show_molin;
          this.sz_ml_money = "0." + (this.sz_hj_money + "").split(".")[1];
          if (this.sz_molin_value) {
            this.sz_title1_molin_open = "开启";
          } else {
            this.sz_title1_molin_open = "未开启";
          }
        },
        yejian_miandarao: function () {
          if (this.yejian_miandarao_value) {
            this.sz_title1_yejian_open = "开启";
          } else {
            this.sz_title1_yejian_open = "未开启";
          }
        },
        dayin1_click: function () {
          alert("不要点我，我有脾气");
        },
        dayin2_click: function () {
          alert("不要点我，我有脾气");
        },
        dayin1_duan_click: function () {
          this.dayin1 = !this.dayin1;
          this.dayin1_duan = !this.dayin1_duan;
        },
        dayin2_duan_click: function () {
          this.dayin2 = !this.dayin2;
          this.dayin2_duan = !this.dayin2_duan;
        },
      },
    });
  </script>
</html>
