div {
  -moz-user-select: none; /*mozilar*/
  -webkit-user-select: none; /*webkit*/
  -ms-user-select: none; /*IE*/
  user-select: none;
}

/*暂不用功能隐藏样式*/
.visibility_class {
  visibility: hidden !important;
}

.menu {
  height: 60px;
  background-color: #ebeef7;
  display: flex;
}

.menu_font {
  width: 80%;
  display: flex;
  margin-left: 100px;
}

.menu_font .menu_font_nav {
  width: 20%;
  min-width: 120px;
  height: 100%;
  color: #ffffff;
  font-size: 18px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
}

.cashier {
  background: #2b282c;
  opacity: 0.95;
  height: 60px;
  padding: 0% 10% 0% 10%;
  display: flex;
  justify-content: space-around;
}

.cashier .li1 {
  display: flex;
  justify-content: center;
  align-items: center;
}

.li1_img {
  width: 40px;
  height: 40px;
}

.cashier .li2 {
  color: white;
  font-size: 16px;
  text-align: center;
  line-height: 60px;
}

/*以上是导航条样式*/
.main {
  display: flex;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

/* 主内容区域容器 */
.main-content {
  position: relative;
  flex: 1;
  overflow: hidden;
  height: 100%;
}

.left {
  width: 100px;
  height: 100%;
}

.left_menu {
  width: 100px;
  height: 100vh;
  background: linear-gradient(180deg, #eaedf6 72%, #eeeaff 100%);
  box-sizing: border-box;
  padding: 40px 0 40px 10px;
  position: relative;
  overflow: auto;
}

.left_menu ul {
  position: relative;
  z-index: 1;
}

.left_menu ul li {
  color: #1c2024;
  font-size: 16px;
  height: 60px;
  text-align: center;
  line-height: 60px;
  cursor: pointer;
  border-radius: 6px 0px 0px 6px;
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter,
    backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.left_menu ul li:hover {
  background: rgba(0, 0, 0, 0.05);
}

.left_menu ul li.addclass {
  background: none;
}

.f-left-nav-bg {
  position: absolute;
  left: 10px;
  width: 90px;
  height: 60px;
  background: #fff;
  z-index: 0;
  border-radius: 6px 0px 0px 6px;
  box-shadow: 0px 7px 10px -5px rgb(62 99 221 / 30%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 主体--右侧 */
.main-right {
  width: 100%;
  height: 100%;
  display: flex;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  will-change: transform, opacity;
  backface-visibility: hidden;
  transform-style: preserve-3d;
}

.server {
  width: 400px;
  height: calc(100vh);
  border-right: 4px solid #ebeef7;
}

.server_chioce {
  width: 400px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #3e63dd;
  box-sizing: border-box;
  border-bottom: 2px solid rgba(238, 238, 238, 1);
}

.server_chioce_give {
  width: 100%;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #3e63dd;
  box-sizing: border-box;
  /* border-bottom: 2px solid rgba(238, 238, 238, 1); */
}

.server_center {
  display: flex;
}

.b_left {
  border: 1px solid #3e63dd;
  border-radius: 4px 0px 0px 4px;
  text-align: center;
  color: #3e63dd;
  line-height: 20px;
  padding: 8px 20px;
  cursor: pointer;
}

.b_right {
  border: 1px solid #3e63dd;
  border-radius: 0px 4px 4px 0px;
  text-align: center;
  color: #3e63dd;
  line-height: 20px;
  padding: 8px 20px;
  cursor: pointer;
}

.b_mid_nocard {
  border: 1px solid #3e63dd;
  border-radius: 0px 4px 4px 0px;
  text-align: center;
  color: #3e63dd;
  line-height: 20px;
  padding: 8px 20px;
  cursor: pointer;
}

.b_mid_card {
  border: 1px solid #3e63dd;
  text-align: center;
  color: #3e63dd;
  line-height: 20px;
  padding: 8px 20px;
  cursor: pointer;
}

.server_bg {
  background-color: #3e63dd;
  color: #ffffff;
}

.server_line1 {
  width: 100%;
  height: 2px;
  background: rgba(238, 238, 238, 1);
}

.search_menu {
  /*height: calc(100vh - 202px);*/
  box-sizing: border-box;
  padding: 15px 15px 0 15px;
}

.search_menu_give {
  width: 100%;
  box-sizing: border-box;
  padding: 15px 15px 0 15px;
}

.search_bor {
  width: 100%;
  margin-bottom: 10px;
}

.search_label {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  margin: auto;
}

.search_label {
  /*display: flex;*/
}

.chooseLabelWrap {
  height: 40px;
}

.chooseLabel {
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
}

.chooseLabel::-webkit-scrollbar {
  height: 8px;
  overflow: hidden;
}

.chooseLabel::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  height: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.chooseLabel::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.chooseLabel > li {
  display: inline-block;
  margin-right: 10px;
}

.open_order_search {
  border: 1px solid #999999;
  color: #999999;
  border-radius: 4px;
  padding: 8px 15px;
  font-size: 14px;
  cursor: pointer;
}

.bg_label {
  color: #fff;
  border: 1px solid #3e63dd;
  background: #3e63dd;
}

.search_detail {
  width: 100%;
}

.serverWrap {
  height: calc(100vh - 130px);
  box-sizing: border-box;
  overflow-y: auto;
}

.serverWrap::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.serverWrap::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.serverWrap::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.serverWrap > li {
  display: inline-block;
  margin-right: 10px;
}

/* .serverWrap_give{
    height: calc(100vh - 360px);
    overflow-y: auto;
} */

.serverWrap_give {
  height: 390px;
  overflow-y: auto;
}
/* @media screen and (min-width: 1301px) {
    .serverWrap_give{
        height: calc(100vh - 360px);
        overflow-y: auto;
    }
} */

.serverWrap_give::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.serverWrap_give::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.serverWrap_give::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.serverWrap_give > li {
  display: inline-block;
  margin-right: 10px;
}

.search_detail1 {
  width: 100%;
  cursor: pointer;
  display: flex;
  box-sizing: border-box;
  padding: 10px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter,
    backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.search_detail1:hover {
  background: #f5f7fa;
}

.search_detail1:active {
  color: #fff;
  background-color: #3e63dd;
}

.serach_detail_info {
  display: flex;
  align-items: center;
}

.serach_detail_img {
  width: 80px;
  height: 80px;
  box-sizing: border-box;
  /*border: 1px solid #f6f6f6;*/
  display: inline-block;
  margin-right: 8px;
  object-fit: contain;
}

.service_name-price {
  width: calc(100% - 78px);
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}
.loadingtip {
  height: 20px;
  line-height: 20px;
  text-align: center;
  margin: 10px 0 5px;
  font-size: 14px;
  color: rgb(77, 76, 76);
}

.service_name {
  width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.service_name1 {
  width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-top: 5px;
}

.service_name_give {
  width: 220px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.custom_dialog_give {
  height: 592px;
  width: 900px;
}

.custom_dialog_give .el-dialog__body {
  padding: 0;
  height: 78vh;
  display: flex;
  flex-direction: row;
  overflow: hidden;
}

.serach_detail_info_font {
  padding: 20px 0px 0px 20px;
}

.serach_detail_info_font1 {
  font-weight: bold;
  font-size: 18px !important;
}

.serach_detail_info_font2 {
  text-align: right;
  font-size: 18px !important;
}

.open_details {
  flex: 1;
  height: 100%;
}

.open_details_border {
  height: 60px;
  box-sizing: border-box;
  border-bottom: 2px solid rgba(238, 238, 238, 1);
  position: relative;
}
.open_details_bordercz {
  height: 60px;
  box-sizing: border-box;
  border-bottom: 2px solid rgba(238, 238, 238, 1);
  position: relative;
}

.open_details_title {
  text-align: center;
  font-size: 16px;
  font-weight: 400;
  line-height: 60px;
}

.open_details_title_font2 {
  position: absolute;
  top: 14px;
  right: 20px;
  font-weight: 400;
  color: #3e63dd;
  border: 1px solid #3e63dd;
  border-radius: 4px;
  padding: 8px 20px;
  font-size: 14px;
  cursor: pointer;
  width: 56px;
}

.open_details_info {
  box-sizing: border-box;
  /*padding: 15px 0;*/
  /* margin-bottom: 15px; */
}
.detailsHeight_give {
  /* 62 + 70 + 60 + 30+58 -60 */
  height: 405px;
  overflow: auto;
}

.detailsHeight {
  /* 62 + 70 + 60 + 30+58 -60 */
  height: calc(100vh - 262px);
  overflow: auto;
}

.detailsHeight2 {
  /* 62 + 70 + 60 + 30+58 -60*/
  height: calc(100vh - 333px);
  overflow: auto;
}

.open_details_info::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.open_details_info::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.open_details_info::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.search_open {
  width: 100%;
  box-sizing: border-box;
  padding: 15px;
}

.search_opne_input {
  font-size: 14px;
  font-weight: 400;
  color: rgba(187, 187, 187, 1);
  border: 0;
  outline: none;
  background-color: rgba(0, 0, 0, 0);
  flex: 1;
}

.search_open img {
  width: 20px;
  height: 20px;
  /* cursor: pointer; */
}

.open_details_price {
  display: flex;
  align-items: center;
  padding-right: 15px;
}

.open_shop {
  flex: 1;
  height: 100%;
  box-sizing: border-box;
  padding: 20px;
  border-left: 6px solid #3e63dd;
  cursor: pointer;
}

.open_server_name {
  width: 100%;
  height: calc(100vh - 511px);
  box-sizing: border-box;
  padding: 0 15px;
  overflow: auto;
}

.open_server_name_give {
  width: 100%;
  max-height: 500px;
  box-sizing: border-box;
  padding: 0 15px;
  overflow: auto;
}

.open_server_name::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.open_server_name::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.open_server_name::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.open_server_name .open_details_price {
  background: #f5f5f5;
}

.open_details_price_bg {
  background: #edf2fe;
}

.open_details_price_line {
  background: #f5f5f5;
  border: 1px solid #f5f5f5;
  width: 4px;
}

.open_details_price_line_bg {
  background: #3e63dd;
  border: 1px solid #3e63dd;
}

.font-weight {
  font-weight: 400;
}

.open_details_price_name,
.open_details_price_num,
.open_details_price_all {
  flex: 1;
}

.open_details_price_name {
  font-size: 16px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.open_details_price_name_give {
  font-size: 16px;
  line-height: 35px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.open_details_price_num {
  font-size: 14px;
  font-weight: 400;
  color: black;
}

.open_details_price_num img {
  width: 18px;
  height: 18px;
  margin-top: 4px;
}

.open_details_price_all {
  min-width: 150px !important;
  font-size: 14px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.open_details_price_all_give {
  min-width: 130px !important;
  font-size: 14px;
  line-height: 35px;
  font-weight: 400;
  margin-left: 24px;
  color: rgba(51, 51, 51, 1);
}

.el-input-number--mini .el-input-number__decrease,
.el-input-number--mini .el-input-number__increase {
  width: 28px;
  margin-top: 1px;
  font-size: 12px;
}

.open_details_price_all img {
  width: 18px;
  height: 18px;
  margin-top: 4px;
}

.open_details_price_del {
  cursor: pointer;
  line-height: 60px;
}

.change_all_price {
  box-sizing: border-box;
  padding: 0 15px;
  border-left: 6px solid #3e63dd;
}

.useOffer,
.subtotal-price {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  box-sizing: border-box;
  padding: 15px 0;
}

.useOffer,
.subtotal_price_give {
  display: flex;
  align-items: center;
  /* justify-content: space-between; */
  font-size: 14px;
  box-sizing: border-box;
  padding: 15px 0;
}

.useOffer {
  padding-bottom: 0;
}

.subtotal-price-inner {
  flex: 1;
  border: 0;
  outline: 0;
}

.offer-type {
  color: #3e63dd;
  cursor: pointer;
}

.change_all_price_line {
  background: #3e63dd;
  border: 1px solid #3e63dd;
  width: 4px;
}

.change_all_price_font1 {
  color: #999999;
}

.change_all_price_font2 {
  flex: 1;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.change_all_price_font2 img {
  width: 15px;
  height: 15px;
  color: #333333 !important;
  margin-top: 1px;
}

.change_all_price_font2 span {
}

.change_all_price_font3 {
  color: #999999;
  font-size: 14px;
}

.change_all_price_font3:hover {
  cursor: pointer;
}

.chioce_technician {
  display: flex;
  border-left: 6px solid #3e63dd;
}

.chioce_technician_line {
  background: #3e63dd;
  border: 1px solid #3e63dd;
  width: 5px;
}

.chioce_technician_name,
.selective_sales_volume,
.batch {
  flex: 1;
  box-sizing: border-box;
  border: 1px solid #f5f5f5;
  box-sizing: border-box;
  padding: 10px 15px;
}

.chioce_technician_name_font1 {
  font-size: 14px;
  font-weight: 400;
  color: #3e63dd;
  cursor: pointer;
}

.chioce_technician_name_font1:hover {
  cursor: pointer;
}

.chioce_technician_name_font2 {
  font-size: 14px;

  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  margin-left: 23px;
}

.selective_sales_volume_font1 {
  font-size: 14px;

  font-weight: 400;
  color: #3e63dd;
}

.selective_sales_volume_font1:hover {
  cursor: pointer;
}

.selective_sales_volume_font2 {
  font-size: 14px;

  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  margin-left: 23px;
}

.batch_font1 {
  font-size: 14px;

  font-weight: 400;
  color: #3e63dd;
  cursor: pointer;
}

.batch_font2 {
  font-size: 14px;

  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  margin-left: 23px;
}

.order_remark {
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
  box-sizing: border-box;
  padding: 0 15px;
}

.remark_input {
  font-size: 14px;
  width: 100%;
  font-weight: 400;
  color: rgba(187, 187, 187, 1);
  border: 0;
  outline: none;
  background-color: rgba(0, 0, 0, 0);
}

.zhk_remark_input {
  font-size: 14px;
  width: 97%;
  font-weight: 400;
  color: rgba(187, 187, 187, 1);
  border: 0;
  outline: none;
  background-color: rgba(0, 0, 0, 0);
  border: 1px solid;
  padding: 6px 12px;
}

.order_remark_font {
  height: 40px;
  line-height: 40px;
  font-size: 14px;
}

/*.order_remark_input {*/
/*height: 30px;*/
/*box-sizing: border-box;*/
/*padding: 0 15px;*/
/*border: 1px solid #E5E5E5;*/
/*}*/

/* .remark_input {
    font-size: 14px;
} */

.open_details_pay {
}

.chioce_Discount {
  height: 58px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  padding-left: 15px;
  border: 1px solid #e5e5e5;
}
.zhk_chioce_Discount {
  /* height: 58px; */
  /* display: flex; */
  /* justify-content: space-between; */
  /* align-items: center; */
  /* box-sizing: border-box; */
  /* padding-left: 30px; */
  border: 1px solid #e5e5e5;
}
.chioce_Discount_font1 {
  width: 50%;
  height: 34px;
  line-height: 34px;
  font-size: 18px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
}

.chioce_Discount_font0 {
  font-size: 16px;
  cursor: pointer;
}

.chioce_Discount_font2 {
  width: 60px;
  height: 20px;
  font-size: 14px;
  font-weight: 400;
  color: #3e63dd;
  cursor: pointer;
}

.chioce_Discount_font3 {
  width: 20px;
  height: 20px;
}

.open_details_pay_choice {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #e5e5e5;
  box-sizing: border-box;
  padding-left: 30px;
}

.open_details_pay_choice_font4,
.open_details_pay_choice_font2,
.open_details_pay_choice_font3 {
  box-sizing: border-box;
  padding: 20px 50px;
  font-size: 18px;
  font-weight: bold;
  color: rgba(255, 255, 255, 1);
  cursor: pointer;
}

.open_details_pay_choice_font1 {
  color: rgba(51, 51, 51, 1);
  font-size: 18px;
}

.open_details_pay_choice_font2 {
  width: 140px;
  background: rgba(122, 122, 122, 1);
}

.open_details_pay_choice_font3 {
  width: 140px;
  background: rgba(43, 40, 44, 1);
}

.open_details_pay_choice_font4 {
  width: 140px;
  background: #3e63dd;
}

.order_three {
  display: flex;
}

/*模态框 保存*/

.save_ok {
  width: 105px;
  font-size: 20px;
  font-weight: 400;
  color: rgba(255, 255, 255, 1);
  padding: 33px 100px;
  background: rgba(0, 0, 0, 1);
  opacity: 0.6;
  border-radius: 50px;
  position: absolute;
  top: 70%;
  left: 40%;
}

/*模态框 选择技师*/
.xuanze_jishi {
  font-size: 21px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  text-align: center;
  padding-bottom: 24px;
  margin-bottom: 24px;
}

.xuanze_jishi_search {
  /*border: 1px solid rgba(221, 221, 221, 1);*/
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.xuanze_jishi_name {
  display: flex;
  padding: 10px 0px 10px 0px;
  justify-content: space-between;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
}

.xuanze_jishi_name_check {
}

.xuanze_jishi_name_font {
  font-size: 17px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  margin-left: 18px;
  cursor: pointer;
}

.xuanze_jishi_server_font {
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
  padding-right: 15px;
}

.xuanze_jishi_server_switch {
  margin-left: 16px;
}

.over_save {
  display: flex;
  cursor: pointer;
}

.over_save_over {
  flex: 1;
  font-size: 21px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
  border: 1px solid rgba(221, 221, 221, 1);
  border-radius: 0px 0px 0px 7px;
  text-align: center;
  padding: 18px 0px 18px 0px;
}

.over_save_save {
  flex: 1;
  font-size: 21px;
  font-weight: 400;
  background: #3e63dd;
  border-radius: 0px 0px 7px 0px;
  text-align: center;
  padding: 18px 0px 18px 0px;
  color: rgba(255, 255, 255, 1);
}

.xuazne_xiaoshou {
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  margin-bottom: 15px;
}

.xuanze_piliang {
  font-size: 22px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  text-align: center;
  padding-bottom: 48px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
}

.xuanze_piliang_font {
  /*height: calc(100vh - 500px);*/
}

.piliang_jishi {
  font-size: 16px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  padding: 23px 0px 23px 36px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  cursor: pointer;
}

.piliang_xiaoshou {
  font-size: 16px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  padding: 23px 0px 23px 36px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  cursor: pointer;
}

.piliang_both {
  font-size: 16px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  padding: 23px 0px 23px 36px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  cursor: pointer;
}

.qudan_font {
  font-size: 21px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.detail_say {
  background: rgba(246, 246, 246, 1);
  border-radius: 4px;
  padding: 10px 30px 10px 30px;
}

.detail_ul {
  display: flex;
  justify-content: space-between;
}

.detail_ul li {
  font-size: 17px;
  font-weight: 400;
  color: rgba(102, 102, 102, 1);
}

.qu_dan {
  font-size: 22px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  text-align: center;
  padding-bottom: 48px;
}

.shops {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid rgba(238, 238, 238, 1);
}

.shops_num {
  /*flex:1;*/
  padding: 12px 0px 12px 30px;
  font-size: 13px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.server_name {
  /*flex:1;*/
  padding: 21px 0px 21px 50px;
  font-size: 13px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  text-align: center;
  line-height: 38px;
}

.shops_name {
  /*flex:1;*/
  padding: 21px 0px 21px 20px;
  font-size: 13px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.shops_name_font {
  padding: 5px 0px 0px 0px;
}

.shops_servername {
  /*flex:1;*/
  padding: 21px 30px 21px 0px;
  font-size: 13px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.shops_servername_font {
  padding: 5px 0px 0px 0px;
}

.shops_price {
  /*flex:1;*/
  padding: 21px 0px 21px 0px;
  font-size: 13px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  text-align: center;
  line-height: 38px;
}

.shops_get_money {
  margin: 15px 5px 15px 0px;
  padding: 10px 17px;
  font-size: 13px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  background: #3e63dd;
  border-radius: 4px;
  text-align: center;
  line-height: 38px;
  cursor: pointer;
}

/*现金交易*/
.zj_xian_jin {
  width: 100%;
  /*margin: auto;*/
}

.receipt-wrap {
  width: 100%;
  height: calc(100vh - 60px);
}

.key-wrap {
  height: calc(100vh - 61px);
}

.zj_show_price {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(221, 221, 221, 1);
  box-sizing: border-box;
  padding: 8px 25px;
  width: 100%;
}

.zj_show_price_font1 {
  font-size: 18px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
}

.zj_font2_label {
  color: #3e63dd;
}

.zj_show_price_font2 {
  flex: 1;
  display: flex;
  align-items: center;
  font-size: 26px;
  color: rgba(51, 51, 51, 1);
}

.zj_show_price_font2 > input {
  border: 0;
  outline: none;
  background-color: rgba(0, 0, 0, 0);
  font-size: 26px;
  font-weight: bold;
  color: rgba(51, 51, 51, 1);
  width: 100%;
}

/*计算输入值*/
.zj_price_menu {
  /*120 46 55*/
  height: calc(100vh - 221px);
  display: flex;
  align-items: center;
  width: 100%;
}

.zj_menu_num {
  width: 76%;
}

.zj_menu_take {
  width: 24%;
}

.zj_one_zero,
.zj_two_zero,
.zj_xiao_dian,
.zj_num_1,
.zj_num_2,
.zj_num_3 {
  width: 32%;
  height: 11vh;
  line-height: 11vh;
  background: rgba(246, 246, 246, 1);
  border: 1px solid rgba(221, 221, 221, 1);
  text-align: center;
  font-size: 36px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8px;
  margin-right: 1%;
  cursor: pointer;
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter,
    backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.zj_one_zero:hover,
.zj_two_zero:hover,
.zj_xiao_dian:hover,
.zj_num_1:hover,
.zj_num_2:hover,
.zj_num_3:hover,
.zj_menu_take_font1:hover,
.iconfont.iconshanchuyigeshuzi:hover {
  box-shadow: 8px 8px 20px -8px rgba(16, 66, 232, 0.2) inset;
}
.zj_menu_take_font2:hover {
  box-shadow: 8px 8px 20px -5px rgb(13, 58, 204) inset;
}

.zj_menu_take_font1,
.iconshanchuyigeshuzi,
.zj_menu_take_font2 {
  width: 98.5%;
  height: 11vh;
  line-height: 11vh;
  background: rgba(246, 246, 246, 1);
  border: 1px solid rgba(221, 221, 221, 1);
  text-align: center;
  font-size: 36px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8px;
  cursor: pointer;
}

.iconshanchuyigeshuzi {
  font-size: 36px !important;
}

.zj_menu_take_font2 {
  height: 23.2vh;
  line-height: 23.2vh;
  background: #3e63dd;
  color: #fff;
  font-size: 30px;
}

/*收银台充值所有样式*/
/*扫码充值*/
.saoma_chongzhi {
  width: 400px;
  height: 60px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-sizing: border-box;
}

.bt_saoma {
  border: 1px solid #3e63dd;
  color: #3e63dd;
  border-radius: 5px;
  height: 40px;
  line-height: 40px;
  padding: 0px 8px 0px 8px;
  cursor: pointer;
}

.chongzhi_view {
  font-size: 16px;
  font-weight: 400;
}

.chonngzhi_vip_all {
  display: flex;
  justify-content: space-between;
  padding: 15px 0px;
  border-bottom: 1px solid #e5e5e5;
}

.chongzhi_vip {
  flex: 0.5;
  display: flex;
}

.chongzhi_vip_info {
  width: 100px;
  height: 100px;
}

.chongzhi_vip_info1 {
  padding: 10px 0px 10px 40px;
}

.chongzhi_vip_info_font1 {
  font-size: 16px;
  font-weight: bold;
  color: rgba(51, 51, 51, 1);
  padding-bottom: 15px;
}

.chongzhi_vip_info_font2 {
  width: 200px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
  padding-bottom: 10px;
  display: flex;
  justify-content: space-between;
}

.chongzhika_info {
  /*height: calc(100vh - 413px);*/
  overflow: auto;
}

.cz_height {
  /*60 + 62 + 30 + 112.19 + 60*/
  height: calc(100vh - 262px);
}

.cz_height2 {
  /*60 + 62 + 30 + 96 + 60*/
  height: calc(100vh - 248px);
}

.chongzhi_vip_info_font3 {
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
}

.chongzhi_vip_info_font4 {
  flex: 0.5;
  font-size: 20px !important;
  text-align: right !important;
  padding-right: 3px;
  line-height: 90px;
  cursor: pointer;
}

.chaongzhika_name_money {
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  padding: 15px 0;
  border-bottom: 1px solid #e5e5e5;
}

.chongzhika_name_font {
  width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 16px;
  font-weight: bold;
  color: rgba(51, 51, 51, 1);
}

.chongzhika_money_font0 {
  flex: 1;
  text-align: right;
}

.chongzhika_money_font1 {
  font-size: 14px;
  color: rgba(153, 153, 153, 1);
}

.chongzhika_money_font2 {
  box-sizing: border-box;
  padding-right: 50px;
}

.chongzhika_money_font3 {
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: 400;
  color: #3e63dd;
}

.chongzhika_money_font4 {
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
}

.chongzhika_money_font5 {
  width: 50px;
  cursor: pointer;
  text-align: center;
}

.chioce_paynum {
  font-size: 14px;
  font-weight: 400;
  color: #3e63dd;
  padding: 15px 0;
  border-bottom: 1px solid #e5e5e5;
  cursor: pointer;
}

.chongzhi_pay_num {
  padding: 15px 0;
  border-bottom: 1px solid #e5e5e5;
}

.chioce_paynum_font1 {
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
}

.chioce_paynum_font2 {
  font-size: 14px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.vip_pay_cengson {
  padding: 15px 0;
  border-bottom: 1px solid #e5e5e5;
}

.vip_pay_cengson_font1 {
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
}

.vip_pay_cengson_font2 {
  font-size: 14px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.vip_xuanze_xiaoshou {
  padding: 15px 0px 15px 0;
  border-bottom: 1px solid #e5e5e5;
}

.vip_xuanze_xiaoshou1 {
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
  cursor: pointer;
}

.vip_xuanze_xiaoshou2 {
  font-size: 14px;
  font-weight: 400;
  color: #3e63dd;
  cursor: pointer;
}

.vip_xuanze_xiaoshou3 {
}

.vip_add_zengson {
  padding: 15px 0px 15px 25px;
  border-bottom: 1px solid #e5e5e5;
  cursor: pointer;
}

.add_zengson {
  font-size: 14px;
  font-weight: 400;
  color: #3e63dd;
}

/*收款的样式*/

.chongzhi_shoukuan {
}

.chongzhi_shoukuan_font {
  text-align: center;
  width: 140px;
  flex: 1;
  padding: 20px 0px;
  font-size: 18px;
  font-weight: bold;
  color: rgba(255, 255, 255, 1);
  cursor: pointer;
  background: #3e63dd;
}

/*支付二维码*/

.saoma_weweima {
  font-size: 21px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  text-align: center;
  padding-bottom: 10px;
  margin-bottom: 20px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
}

.zhifu_weweima {
  text-align: center;
}

.zhifu_weweima_img {
  width: 400px;
  height: 400px;
}

/*选择充值金额*/

.chongzhi_chongzhi_jine {
  font-size: 21px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  text-align: center;
  padding-bottom: 24px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
}

.chongzhi_jine_mian {
  display: flex;
  max-height: 300px;
  overflow: auto;
  flex-wrap: wrap;
}

.customize {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}

.chongzhi_danka {
  box-sizing: border-box;
  padding: 15px 25px;
  border: 1px solid rgba(229, 229, 229, 1);
  margin: 0 10px;
  margin-bottom: 15px;
  width: 150px;
}

.danka_get {
  font-size: 16px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  margin-bottom: 10px;
}

.danka_post {
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.danka_post:hover {
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
  text-overflow: inherit;
  overflow: visible;
  white-space: pre-line; /*合并空白符序列，但是保留换行符。*/
}

/*充值页面添加服务样式*/

/*收银台办卡所有样式*/

/*办卡的css*/
.presonal_info {
  padding: 6px;
  display: flex;
}
.zhk_presonal_info {
  padding: 14px 20px;
  display: flex;
}

.bk_presonal_touxiang {
  display: flex;
  align-items: center;
}

.presonal_data {
  width: 100%;
  height: 70px;
  padding: 15px;
}

.presonal_tel {
  width: 100%;
  box-sizing: border-box;
  padding: 8px 0;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
}

.tel_input {
  border: 0;
  background: 0;
  outline: none;
  font-size: 14px;
  font-weight: 400;
  /*color: rgba(187, 187, 187, 1);*/
  margin-left: 15px;
}

.presonal_name {
  width: 100%;
  box-sizing: border-box;
  padding: 12px 0;
  display: flex;
  justify-content: space-between;
}

.name_input {
  border: 0;
  background: 0;
  outline: none;
  font-size: 14px;
  font-weight: 400;
  /*color: rgba(187, 187, 187, 1);*/
  margin-left: 15px;
}

.gender {
  display: inline-block;
  padding: 5px 10px;
  color: #3e63dd;
  border: 1px #3e63dd solid;
  cursor: pointer;
}

.bk_sex_add {
  color: white;
  background: #3e63dd;
}

/*卡的信息样式*/
.ka_server_one {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.ka_server_one1 {
  display: flex;
  justify-content: space-between;
  padding-bottom: 20px;
}

.ka_one_font1 {
  text-align: left;
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
}

.ka_one_font2 {
  text-align: right;
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
}

.ka_name_font1 {
  font-size: 14px;
  font-weight: bold;
  color: rgba(51, 51, 51, 1);
}

.ka_name_font2 {
  font-size: 14px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.ka_name_font3 {
  width: 100px;
  text-align: right;
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
}

.iconlajitong {
  width: 100%;
  font-size: 20px !important;
  text-align: center;
  cursor: pointer;
}

.ka_detail {
  margin-top: 10px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
}

/*从充卡copy的增减样式*/
.ka_choice_num {
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
  line-height: 50px;
}

.open_details_num {
  flex: 1;
  height: 50px;
  line-height: 50px;
  font-size: 14px;
  font-weight: 400;
  color: black;
  margin-left: 20px;
}

.span1 {
  padding: 4px 10px 4px 10px;
  background: rgba(246, 246, 246, 1);
  border: 1px solid rgba(229, 229, 229, 1);
  cursor: pointer;
}

.span2 {
  padding: 4px 10px 4px 10px;
  border: 1px solid rgba(229, 229, 229, 1);
  border-left: none;
  border-right: none;
}

.span3 {
  cursor: pointer;
  padding: 4px 8px 4px 8px;
  background: rgba(246, 246, 246, 1);
  border: 1px solid rgba(229, 229, 229, 1);
}

.ka_zeng_jian {
  border-bottom: 1px solid rgba(229, 229, 229, 1);
}

.dist_power {
  display: flex;
  justify-content: space-between;
  line-height: 50px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
}

.dist_power_font2 {
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
}

.dist_power_font3 {
  font-size: 14px;
  font-weight: 400;
  color: #3e63dd;
  cursor: pointer;
}

.xiaoji_price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  padding: 12px 0;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
}

.card-subtotal-money {
  flex: 1;
  display: flex;
  align-items: center;
}

.card-subtotal {
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
  cursor: pointer;
}

.change-money {
  flex: 1;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  box-sizing: border-box;
  margin: 0 10px;
  border-radius: 4px;
  outline: none;
  padding: 4px 0;
}

.tip {
  width: 100px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
}

/*选择销售*/
.chioce_xiaoshou1 {
  display: flex;
  justify-content: space-between;
  line-height: 50px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
}

.chioce_xiaoshou1_font2 {
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
}

.chioce_xiaoshou1_font3 {
  font-size: 14px;
  font-weight: 400;
  color: #3e63dd;
  cursor: pointer;
}

/*添加服务*/

.add_server1 {
  display: flex;
  justify-content: space-between;
  line-height: 50px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
}

.add_server1_font2 {
  font-size: 14px;
  font-weight: 400;
  color: #3e63dd;
}

/*添加服务*/

.tianjia_fuwu {
  font-size: 22px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  text-align: center;
}

.tianjia_fuwu_search {
  border: 1px solid rgba(221, 221, 221, 1);
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  padding: 10px 5px 10px 5px;
  margin-top: 10px;
}

.tianjia_fuwu_input {
  border: 0;
  outline: none;
  background: 0;
}

.tianjia_fuwu_mian {
  display: flex;
  margin-top: 10px;
  height: calc(100vh - 500px);
  overflow: auto;
}

.fuwu_biaoti {
  width: 20%;
}

.fuwu_biaoti_chioce {
  width: 79%;
}

.tianjia_fuwu_font {
  font-size: 14px;
  font-weight: 400;
  color: rgba(102, 102, 102, 1);
  padding: 10px 10px 10px 10px;
  border-bottom: 0.5px solid rgba(221, 221, 221, 1);
}

.tianjia_fuwu_font0 {
  background: #ebdcf2;
  font-size: 14px;
  font-weight: 400;
  color: #3e63dd;
  padding: 10px 10px 10px 10px;
  border-bottom: 0.5px solid rgba(221, 221, 221, 1);
}

.fuwu_biaoti_line {
  /*width: %;*/
  border: 0.5px solid rgba(221, 221, 221, 1);
}

.fuwu_biaoti_chioce {
}

.fuwu_biaoti_chioce_bottom {
  border-bottom: 0.5px solid rgba(221, 221, 221, 1);
}

.server_biaoti_name_font {
  height: 30px;
  line-height: 30px;
  padding: 10px 0px 10px 0px;
  display: flex;
  justify-content: space-between;
}

.server_biaoti_name_font1 {
}

.server_biaoti_name_font2 {
  font-size: 14px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.server_biaoti_name_font3 {
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
  text-align: right;
}

/*选择优惠权益*/

.select-offer > .el-dialog {
  margin: 0 auto;
}

.xuanze_qunayi {
  font-size: 22px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  text-align: center;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
}

.xuanze_qunayi_font0 {
  font-size: 14px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  padding: 20px 0px 20px 30px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  cursor: pointer;
}

.xuanze_qunayi_font1 {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  cursor: pointer;
}

.xuanze_qunayi_font2 {
  padding: 20px 0px 20px 30px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.xuanze_qunayi_font3 {
  padding: 20px 20px 20px 30px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
}

.use-offer-ul {
  max-height: 300px;
  overflow: auto;
}

.use-offer-ul::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.use-offer-ul::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.use-offer-ul::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.use-offer-li {
  display: flex;

  padding: 20px 0px 20px 30px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  cursor: pointer;
}

.offer-cardName {
  display: inline-block;
  width: 150px;
  white-space: normal;
}

.coupon-cardName {
  display: flex;
  flex-direction: column;
  width: 230px;
  margin-right: 10px;
  white-space: normal;
}

.use-offer-li-coupon {
  /* display: flex; */
  padding: 20px 0px 10px 20px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  cursor: pointer;
}

/*选择优惠样式*/

.xuanze_youhui_font1 {
  font-size: 22px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  text-align: center;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
}

.xuanze_youhui_font2 {
  padding: 20px 0px 20px 30px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  border-bottom: 1px solid rgba(229, 229, 229, 1);
}

.xuanze_youhui_font3 {
  padding: 20px 0px 20px 30px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  margin-bottom: 200px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
}

/*收银台充卡所有样式*/

.zhk_img1 {
  width: 40px;
  height: 40px;
}

.zhk_presonal_touxiang {
  display: flex;
  align-items: center;
}

.zhk_presonal_touxiang_img {
  width: 100px;
  height: 100px;
}

.zhk_serach_img {
  display: flex;
  align-items: center;
}

.zhk_serach_detail_font {
  padding-left: 20px;
}

.zhk_open_details_info {
  padding: 20px 3vh;
}

.zhk_presonal_data {
  padding-left: 20px;
  width: 100%;
}

.zhk_tel_input {
  width: 100%;
  border: 0;
  background: 0;
  outline: none;
  font-size: 14px;
  font-weight: 400;
  color: rgba(187, 187, 187, 1);
}

.zhk_name_input {
  width: 70%;
  border: 0;
  background: 0;
  outline: none;
  font-size: 14px;
  font-weight: 400;
  color: rgba(187, 187, 187, 1);
}

.zhk_open_server_name {
  margin: auto;
  height: calc(100vh - 428px);
  overflow: auto;
  padding-top: 20px;
}

.zhk_open_shop {
  display: flex;
  width: 100%;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 15px 15px;
  /* border-top: 1px solid rgba(229, 229, 229, 1); */
  border-bottom: 1px solid rgba(229, 229, 229, 1);
}

.zhk_open_details_price_name {
  flex: 1;
  font-size: 16px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  padding-top: 5px;
}

.zhk_open_details_num {
  flex: 1;
  height: 50px;
  line-height: 50px;
  font-size: 14px;
  font-weight: 400;
  color: black;
}

.zhk_open_details_price_name1 {
  padding: 10px 0px;
}

.zhk_open_details_prices {
  flex: 1;
  line-height: 60px;
  text-align: center;
}

.zhk_chioce_Discount_font2 {
  font-size: 14px;
  font-weight: 400;
  color: #3e63dd;
  margin-top: -25px;
  margin-left: 100px;
}

.chioce_Discount_font1_check {
  flex: 1;
  /* text-align: center; */
}
.zhk_chioce_Discount_font1 {
  flex: 1;
}

.zhk_chioce_Discount1 {
  height: 58px;
  display: flex;
  /* justify-content: space-between; */
  align-items: center;
  box-sizing: border-box;
  padding-left: 30px;
  border: 1px solid #e5e5e5;
}

.zhk_Discount_font1_input1,
.zhk_Discount_font1_input2 {
  border: 1px solid rgba(153, 153, 153, 1);
  background: 0;
  outline: none;
  background: white;
  padding: 8px 16px;
  border-radius: 3px;
  /* width: 60%; */
  color: #999999;
  font-size: 14px;
}

.zhk_chioce_Discount2 {
  height: 58px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  padding-left: 30px;
  border: 1px solid #e5e5e5;
}

.zhk_xingbeinv {
  color: #3e63dd;
  height: 25px;
  line-height: 25px;
  padding: 2px 2px 2px 2px;
  border: 1px #3e63dd solid;
  cursor: pointer;
  /*background: #B35DCC;*/
}

.zhk_xingbeinan {
  height: 25px;
  line-height: 25px;
  padding: 2px 2px 2px 2px;
  border: 1px #3e63dd solid;
  cursor: pointer;
  /*background: #B35DCC;*/
  color: #3e63dd;
}

.zhk_sex_add {
  color: white;
  background: #3e63dd;
}

/*y一下是办卡css样式*/

.bk_server_center1 {
  border: 0.5px solid #3e63dd;
  border-radius: 8px 0px 0px 8px;
  text-align: center;
  color: #3e63dd;
  line-height: 20px;
  padding: 3px 10px 3px 10px;
  cursor: pointer;
}

.bk_server_center2 {
  border: 0.5px solid #3e63dd;
  text-align: center;
  color: #3e63dd;
  line-height: 20px;
  padding: 3px 10px 3px 10px;
  cursor: pointer;
}

.bk_server_center3 {
  border: 0.5px solid #3e63dd;
  text-align: center;
  color: #3e63dd;
  line-height: 20px;
  padding: 3px 10px 3px 10px;
  cursor: pointer;
}

.bk_server_center4 {
  border: 0.5px solid #3e63dd;
  border-radius: 0px 8px 8px 0px;
  text-align: center;
  color: #3e63dd;
  line-height: 20px;
  padding: 3px 10px 3px 10px;
  cursor: pointer;
}

.bk_color_ka {
  background: #3e63dd !important;
  color: white !important;
}

.bk_open_details {
  width: 100%;
}

.bk_presonal_touxiang_img {
  width: 90px;
  height: 90px;
  display: block;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  border-radius: 50px;
}

.bk_serach_img {
  display: flex;
  align-items: center;
}

.bk_serach_detail_font {
  padding-left: 20px;
}

.bk_open_details_info {
  height: calc(100vh - 177px);
  box-sizing: border-box;
  overflow: auto;
  padding: 11px 12px;
}

.bk_open_details_info::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.bk_open_details_info::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.bk_open_details_info::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.bk_presonal_data {
  padding-left: 20px;
  width: 100%;
}

.bk_tel_input {
  width: 100%;
  border: 0;
  background: 0;
  outline: none;
  font-size: 14px;
  font-weight: 400;
  caret-color: #3e63dd;
}

.bk_name_input {
  border: 0;
  background: 0;
  outline: none;
  font-size: 14px;
  font-weight: 400;
  caret-color: #3e63dd;
  flex: 1;
}

.bk_open_server_name {
  /*height: calc(100vh - 428px);*/
}

/*以下是充值css样式*/

.chongzhi_vip_info_img {
  width: 100px;
  height: 100px;
}

.cz_open_details_info {
  padding: 16px 12px;
}

.zj_font1 {
  height: 60px;
  text-align: center;
  font-size: 18px;
  font-weight: 400;
  color: rgba(0, 0, 0, 1);
  border-bottom: 1px solid rgba(238, 238, 238, 1);
  position: relative;
}

.zj_font2 {
  width: 100%;
  box-sizing: border-box;
  padding: 15px 0;
  text-align: center;
  font-size: 16px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
}

.zj_xuanze_vip {
  position: absolute;
  top: 15px;
  right: 20px;
  padding: 8px 20px;
  font-size: 14px;
  font-weight: 400;
  color: #3e63dd;
  border: 1px solid #3e63dd;
  border-radius: 4px;
  cursor: pointer;
}

.zj_font4 {
  font-size: 18px;
  font-weight: 400;
  color: rgba(0, 0, 0, 1);
  border-bottom: 1px solid rgba(238, 238, 238, 1);
  line-height: 60px;
}

.cashier_open_order_Specifications_img {
  width: 70px;
  height: 70px;
  box-sizing: border-box;
  padding: 5px;
  border: 1px solid #f6f6f6;
}

.C_open_order_Specifications1 {
  display: flex;
  margin-bottom: 15px;
}

.C_open_order_Specifications_font {
  padding: 10px 0px 10px 20px;
}

.C_open_order_Specifications_font1 {
  font-size: 14px;
  font-weight: 400;
  color: #333333;
}

.C_open_order_Specifications_font2 {
  font-size: 12px;
  font-weight: 400;
  color: #999999;
  padding-top: 10px;
}

.C_open_order_Specifications2 {
  padding: 10px 0;
  border-bottom: 1px solid #e5e5e5;
}

.C_open_order_Specifications2_font1 {
  padding-bottom: 5px;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
}

.C_open_order_Specifications2_font2 {
  padding: 10px 0px 5px 0px;
  font-size: 12px;
  font-weight: 400;
  color: #999999;
  display: flex;
}

.C_open_order_Specifications2_font3 {
  margin-right: 10px;
  padding: 8px 20px;
  border: #999999 1px solid;
  border-radius: 4px;
  cursor: pointer;
}

.C_open_order_Specification2 {
  max-height: 300px;
  overflow: auto;
  margin-bottom: 30px;
}

.C_Specifications_choice1_add {
  background: #3e63dd !important;
  color: white !important;
  border: 1px solid #3e63dd !important;
}

.open_details_price_name_font1 {
  font-size: 14px;
  font-weight: 400;
  color: black;
}

.open_details_price_name_font2 {
  padding-top: 5px;
  font-size: 12px;
  font-weight: 400;
  color: #999999;
}
.open_details_price_name_font3 {
  /*padding-top: 5px;*/
  font-size: 14px;
  font-weight: 400;
  color: #ee6911;
  padding: 0px 0px 10px 20px;
  border-left: 6px solid #3e63dd;
  cursor: pointer;
}

.el-input__icon {
  cursor: pointer;
}

.servers_none_show_y {
  display: block;
}

.servers_none_show_n {
  display: none;
}

.cancel-btn:focus,
.cancel-btn:hover {
  color: #3e63dd;
  border-color: #dcdfe6;
  background-color: #fff;
}

/* 5- 30 */

.queryMemberInfo {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 15px 0;
  border-bottom: 1px solid #dcdfe6;
}

/*充卡会员样式*/
.zhkqueryMemberInfo {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 15px 0;
  border-bottom: 1px solid #dcdfe6;
}

/*充卡个人信息样式*/
.zuk_presonal_info {
  padding: 20px;
  display: flex;
}

.member-pic {
  width: 80px;
  height: 80px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  border-radius: 50px;
  margin-right: 15px;
}

.member-pic > img {
  display: inline-block;
  width: 100%;
  height: 100%;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  border-radius: 50px;
}

.member-info-wrap {
  flex: 1;
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.member-name {
  width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 600;
  font-size: 18px;
}

.member-info {
  flex: 1;
  line-height: 20px;
}

.member-del {
  text-align: center;
  width: 50px;
}

/*取单*/

/*下单收款*/
.cz_qudan_top {
  display: flex;
  align-items: center;
  width: 100%;
  height: 60px;
  box-sizing: border-box;
  padding: 0 35px;
  /* background: #2b282c; */
  /* color: #fff; */
  font-size: 18px;
}

.title-left,
.left_main {
  width: 400px;
}

.left_main{
  border-right: 1px solid rgb(243 244 246);
}

.cash-register {
  display: flex;
}

.left_mid {
  padding: 15px 20px;
  height: calc(100vh - 120px);
  box-sizing: border-box;
  border-right: 3px solid #f6f6f6;
  overflow-y: auto;
}

.left_mid::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.left_mid::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.left_mid::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.order_info_wrap {
  display: flex;
}

.order_info_index {
  box-sizing: border-box;
  margin-right: 8px;
}

.order_item {
  flex: 1;
  border-bottom: 1px solid #e5e5e5;
  margin-bottom: 8px;
}

.order_info_li {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.code-attention {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.border-margin {
  border-bottom: 2px solid #f6f6f6;
  margin-bottom: 10px;
}

.shoucang_img {
  width: 100px;
  height: 100px;
  margin-bottom: 10px;
}

.shoucang_img > img {
  width: 100%;
  height: 100%;
}

.left_but {
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  padding: 0 15px;
  height: 60px;
  background: #f6f6f6;
  font-size: 18px;
  color: #000;
}

.right_main {
  width: calc(100vw - 400px);
  /* height: calc(100vh - 60px); */
}

.cash-register-type {
  box-shadow: 0 0 10px 0 inset rgba(0, 0, 0, 0.1);
}

.f-line-b{
  flex: 1;
  box-sizing: border-box;
  border-bottom: 1px solid rgb(243 244 246 );
}

.f-line-b-l{
  box-shadow: 25px 0px 20px 0px #FFF inset, 0px -15px 15px -18px rgba(0, 0, 0, 0.15) inset;
}

.f-line-b-r{
  box-shadow: -25px 0px 20px 0px #FFF inset, 0px -15px 15px -18px rgba(0, 0, 0, 0.15) inset;
}

.pay-type {
  width: 150px;
  height: 50px;
  line-height: 50px;
  background: #f6f6f6;
  text-align: center;
  margin: 0 10px;
  font-size: 16px;
  cursor: pointer;
}

.debt {
  position: absolute;
  top: 88px;
  right: 20px;
}

.pay-typeActive {
  background: #fff;
  /* color: #3e63dd; */
  border-radius: 10px 10px 0 0;
  box-shadow: -5 0 10px -3 inset rgba(0, 0, 0, 0.1);
}

.pay-key-wrap {
  height: calc(100vh - 166px);
}

.zj_font3 {
  width: 80%;
  margin: 0 auto;
}

.receiptMemberInfo {
  box-sizing: border-box;
  padding: 15px;
  box-shadow: 0px 0px 1px 2px #f6f6f6;
  margin-top: 15px;
}

.member_info {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #333;
}

.member_info > .el-icon-remove {
  margin-right: 10px;
  color: #3e63dd;
  font-size: 20px;
  font-weight: 400;
  cursor: pointer;
}

.receiptMemberInfo > li {
  margin-bottom: 8px;
}

.receiptMemberInfo > :last-child {
  margin-bottom: 0;
}

.cz_kaidan .el-dialog__header {
  padding: 0;
}

.cz_kaidan .el-dialog__body {
  padding: 0;
}

.kd_weixin {
  width: 100%;
  margin: auto;
  text-align: center;
}

.kd_weixin > div:nth-child(1) {
  font-size: 20px;
  color: #333333;
  font-weight: 400;
  margin-top: 30px;
}

.kd_weixin > div:nth-child(2) {
  font-size: 14px;
  margin: 20px 0 30px 0;
  color: #999999;
  font-weight: 400;
}

.kd_weixin > div:nth-child(4) > input {
  width: 350px;
  height: 44px;
  border: 1px solid #3e63dd;
  margin: 30px 0 50px 0;
  text-align: center;
}

.kd_bottom {
  width: 100%;
  text-align: center;
  font-size: 14px;
  color: #3e63dd;
}

.right_font3_title {
  width: 100%;
  font-size: 20px;
  font-weight: 400;
  text-align: center;
  margin: 30px 0 30px 0;
  color: #333333;
}

.kd_server_list_czk {
  width: 45%;
  height: 90px;
  margin-left: 20px;
  margin-bottom: 10px;
  background-color: #f6f6f6;
  border-radius: 5px;
  cursor: pointer;
}

.kd_server_list_czk > div:nth-child(1) {
  font-size: 14px;
  font-weight: bold;
  color: rgba(51, 51, 51, 1);
  padding: 15px 15px;
}

.kd_server_list_czk > div:nth-child(2) {
  color: rgba(51, 51, 51, 1);
  font-size: 22px;
  font-weight: bold;
  padding-left: 13px;
  padding-bottom: 5px;
}

.kd_server_list_czk > div:nth-child(3) {
  font-size: 12px;
  padding-left: 18px;
  color: #bbbbbb;
  padding-bottom: 15px;
}

.kd_yue {
  height: 300px;
  overflow: auto;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}

.kd_yue::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.kd_yue::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.kd_yue::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.kd_czk_list_active {
  border: 1px solid #cccccc;
}

.right_font3_button {
  width: 100%;
  text-align: center;
  margin: auto;
  /*margin-top: 30px;*/
}

.right_font3_button > .el-button--medium {
  padding: 15px 35px;
  font-size: 16px;
  border-radius: 4px;
}

/* 扣卡 */
.search_menu-card {
  /*box-sizing: border-box;*/
  /*padding-top: 15px;*/
}

.consumption-ul {
  display: flex;
  box-sizing: border-box;
  padding: 15px;
}

.consumption-li {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 14px;
}

.consumption-info {
  margin-bottom: 10px;
}

.secondary-card-equity {
  box-sizing: border-box;
  padding: 12px 15px;
  background: #eeeeee;
}

.search_menu-card > .search_detail {
  box-sizing: border-box;
  /* padding: 0 15px; */
  height: calc(100vh - 68px);
  overflow: auto;
}

.search_menu-card > .search_detail::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.search_menu-card > .search_detail::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.search_menu-card > .search_detail::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.newMemberCardGoods {
  border-top: 1px solid #ccc;
  padding: 8px;
  display: flex;
  flex-direction: row;
  position: relative;
}

.newMemberCardInfo .el-collapse-item__content {
  padding-bottom: 0;
}

.newMemberCardItem .newMemberCardGoods:last-child {
  border-bottom: 1px solid #ccc;
  padding: 8px;
  display: flex;
  flex-direction: row;
  position: relative;
}

.newMemberCardGoods .newMemberCardGoodsSercice {
  text-align: left;
}

.newMemberCardGoods .newMemberCardGoodsStatusName {
  position: absolute;
  right: 15px;
  text-align: right;
}

.newMemberCardGoodsWhiteStatusName {
  color: #fff;
  margin-top: 5px;
}

.newMemberCardGoodsSercice > p:first-child {
  margin-top: 10px;
}

.newMemberCardGoodsSercice > p:last-child {
  margin-top: 14px;
}

.newMemberCardGoodsStatusName > p:first-child {
  margin-top: 10px;
}

.newMemberCardGoodsStatusName > p:last-child {
  margin-top: 14px;
}

.card-price-num {
  display: flex;
  justify-content: space-between;
}

.takeOrderName > td {
  font-size: 14px;
}

.el-popup-parent--hidden {
  padding-right: 0 !important;
}

.cancelButtonClass:focus,
.cancelButtonClass:hover {
  border-color: #dcdfe6;
  background-color: #fff;
}

/*充卡的新加样式*/
.zuheka-wrap {
  width: 100%;
  height: 100%;
}
.zhkVipHead {
  width: 80px;
  height: 80px;
  display: inline-block;
  vertical-align: middle;
}
.presonal_touxiang {
  height: 100px;
  line-height: 100px;
}
.vip_sex_show {
  background: #3e63dd;
  border: 1px solid #3e63dd;
  color: white;
  padding: 5px 10px;
  cursor: pointer;
  font-size: 14px !important;
  font-weight: 400;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.vip_sex_none {
  border: 1px solid rgba(229, 229, 229, 1);
  padding: 5px 10px;
  cursor: pointer;
  font-size: 14px !important;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.open_price_name_font1 {
}
.open_price_name_font1 {
  padding-bottom: 10px;
}
.zhk_order_remark_font {
  height: 35px;
  line-height: 40px;
  font-size: 14px;
}
.zhk_order_remark_input {
  box-sizing: border-box;
  /* padding: 0 15px; */
  width: 100%;
  height: 30px;
  /* border: 1px solid; */
  line-height: 24px;
  margin-bottom: 10px;
}

/* 订单列表样式 */
.demo-table-expand .el-form-item {
  margin-bottom: 0;
}

/* 修改员工业绩部分样式 */
.performance_list {
  margin-bottom: 10px;
  /* box-shadow: 0 2px 12px 0 rgba(0,0,0,.1); */
  padding: 8px;
}
.performance_list_item {
  margin-bottom: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 8px;
}
.performance_list > li .performance_card {
  width: 80%;
  height: 40px;
  line-height: 40px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin: auto;
  background-color: #f1e8e8;
  border-radius: 5px;
  margin-bottom: 10px;
}
.performance_title {
  display: flex;
  justify-content: space-around;
  height: 40px;
  line-height: 40px;
  font-size: 16px;
  font-weight: bold;
}
.performance_row {
  display: flex;
  align-items: center;
  padding: 5px 0;
}
.performance_row > div:first-child {
  width: 14%;
  min-height: 30px;
  text-align: center;
}
.performance_row ul {
  width: 100%;
}
.performance_row ul li {
  display: flex;
  justify-content: space-around;
  margin-bottom: 8px;
}
.performance_row .performance_group_input {
  display: flex;
}
.performance_row .performance_group_input .el-input-group {
  width: initial;
}

.performance_row ul li input {
  width: 80px;
}
.performance_row ul li input[type="number"] {
  -moz-appearance: textfield;
}
.performance_row ul li input[type="number"]::-webkit-inner-spin-button,
.performance_row ul li input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.performance_row ul li .choose_deduct input {
  padding: 0;
  width: 83px;
}
/* 选择销售手艺人弹框 */
.kd_add_box > div {
  width: 40%;
  height: 500px;
  overflow: auto;
}
.kd_add_box > div::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}
.kd_add_box > div::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}
.kd_add_box > div::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}
.sales_content {
  display: flex;
  flex-wrap: wrap;
}
.sales_content div {
  margin: 0 10px 10px 0;
}

@media screen and (max-width: 1200px) {
  .zj_show_price1 {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(221, 221, 221, 1);
    box-sizing: border-box;
    padding: 8px 25px;
    width: 81%;
    margin-right: 20px;
  }

  .return-money {
    color: #999;
    margin-top: 200px;
    text-align: center;
    font-size: 20px;
    font-weight: 600;
  }
  .return-money span {
    display: block;
  }

  .return-money-btn {
    margin-top: 100px;
    font-size: 20px;
  }
}

@media screen and (min-width: 1201px) {
  .return-money {
    color: #999;
    margin-top: 300px;
    text-align: center;
    font-size: 30px;
    font-weight: 600;
  }
  .return-money span {
    display: block;
  }

  .return-money-btn {
    margin-top: 150px;
    font-size: 26px;
  }
  .zj_show_price1 {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(221, 221, 221, 1);
    box-sizing: border-box;
    padding: 8px 25px;
    width: 90%;
    margin-right: 20px;
  }
}

.zj_show_price1_font1 {
  font-size: 18px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
}

.zj_font2_label {
  color: #3e63dd;
}

.zj_show_price1_font2 {
  flex: 1;
  display: flex;
  align-items: center;
  font-size: 26px;
  color: rgba(51, 51, 51, 1);
}

.zj_show_price1_font2 > input {
  border: 0;
  outline: none;
  background-color: rgba(0, 0, 0, 0);
  font-size: 26px;
  font-weight: bold;
  color: rgba(51, 51, 51, 1);
  width: 100%;
}

.pay_type_tag {
  border: 1px solid #bbbbbb;
  margin: 0px 20px 20px 0px;
  color: #606266;
  background: #fff;
  padding: 10px 22px;
  height: 52px;
  line-height: 30px;
  font-size: 16px;
  cursor: pointer;
}

.pay_type_active {
  color: #fff;
  background: #3e63dd;
  border: 1px solid #3e63dd;
}

.pay_type_div {
  text-align: -webkit-right;
}

.pay_type_money {
  width: 24%;
  height: 10vh;
  line-height: 10vh;
  background: #3e63dd;
  border: 1px solid rgba(221, 221, 221, 1);
  text-align: center;
  font-size: 24px;
  color: #fff;
  margin-bottom: 8px;
  cursor: pointer;
}

/* 收银台核销 */
.verify_search {
  width: 60%;
  margin: 80px auto 0 auto;
  display: flex;
  flex-direction: row;
}

.verify_input {
  width: 84%;
  height: 58px;
  border: 1px solid #ddd;
  border-radius: 30px 0 0 30px;
}

.verify_input input {
  outline: none;
  border: 0px;
  line-height: 58px;
  margin-left: 36px;
  font-size: 20px;
  width: 100%;
}

.verify_btn {
  width: 16%;
  height: 60px;
  /* border: 1px solid #ddd; */
  border-radius: 0 10px 10px 0;
  background: #3e63dd;
  cursor: pointer;
}

.verify_btn button {
  /* display: inherit; */
  outline: none;
  border: 0px;
  width: 100%;
  height: 60px;
  /* margin-left: 28px; */
  font-size: 20px;
  color: #fff;
}

.verify_display {
  width: 100%;
  margin-top: 80px;
  text-align: center;
}

.verify_span {
  margin-top: 10px;
  cursor: default;
  font-size: 20px;
}

.verify_card {
  width: 60%;
  margin: 36px auto 0 auto;
  box-shadow: 0 0 10px #d1d1d1;
  padding-top: 30px;
}

.verify_server {
  width: 82%;
  margin: auto;
  height: 90px;
  box-shadow: 0 0 10px #d1d1d1;
  overflow: hidden;
  overflow-y: auto;
  padding: 20px 30px;
  display: flex;
  flex-direction: row;
}

.verify_server_name {
  padding-left: 50px;
}

.verify_ul {
  width: 88%;
  margin: auto;
}

.verify_appointment {
  width: 100%;
  line-height: 44px;
  height: 44px;
  border-bottom: 1px solid #d1d1d1;
}

.verify_ul_remark {
  margin-top: 16px;
}

.verify_ul_btn {
  width: 100%;
  margin: 20px auto 0 auto;
  text-align: center;
  padding-bottom: 20px;
  display: flex;
  flex-direction: row;
  justify-content: space-around;
}
.verify_ul_btn button {
  width: 120px;
  height: 30px;
  border: 1px solid #3e63dd;
  /* background: #B35DCC; */
  color: #3e63dd;
  border-radius: 2px;
}
.fetchOrderDialog .el-dialog__body {
  margin-top: 10px;
  padding: 0 20px 30px 20px;
}

.fetchOrderDialog
  .el-table--scrollable-y
  .el-table__body-wrapper::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.fetchOrderDialog
  .el-table--scrollable-y
  .el-table__body-wrapper::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.fetchOrderDialog
  .el-table--scrollable-y
  .el-table__body-wrapper::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.fetchOrderClass {
  display: flex;
  flex-direction: row;
}

.fetchOrderClass .el-date-editor--datetimerange.el-input,
.el-date-editor--datetimerange.el-input__inner {
  width: 330px;
}

/* 查看耗材 */
.costMaterial {
  overflow: hidden;
  overflow-y: auto;
  text-align: left;
  height: 270px;
}

.costMaterial::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.costMaterial::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.costMaterial::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

/* .switchStyle .el-switch__label {
    position: absolute;
    display: none;
    color: #fff;
  }
  .switchStyle .el-switch__label--left {
    z-index: 9;
    left: 6px;
  }
  .switchStyle .el-switch__label--right {
    z-index: 9;
    left: -7px;
  }
  .switchStyle .el-switch__label.is-active {
    display: block;
  }
  .switchStyle.el-switch .el-switch__core,
  .el-switch .el-switch__label {
    width: 50px !important;
  } */

/* 主内容区域动画效果 */
.main-right {
  width: 100%;
  height: 100%;
  display: flex;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  will-change: transform, opacity;
  backface-visibility: hidden;
  transform-style: preserve-3d;
}

/* 主页面切换动效 */


/* 向上过渡效果（从大到小导航） */
.slide-up-enter {
  transform: translateY(30px);
  opacity: 0;
}

.slide-up-enter-active {
  transition: transform 0.1s ease-out, opacity 0.1s ease-out;
}

.slide-up-leave-active {
  transition: transform 0.1s ease-in, opacity 0.1s ease-in;
}

.slide-up-leave-to {
  transform: translateY(-30px);
  opacity: 0;
}

/* 向下过渡效果（从小到大导航） */
.slide-down-enter {
  transform: translateY(-30px);
  opacity: 0;
}

.slide-down-enter-active {
  transition: transform 0.1s ease-out, opacity 0.1s ease-out;
}

.slide-down-leave-active {
  transition: transform 0.1s ease-in, opacity 0.1s ease-in;
}

.slide-down-leave-to {
  transform: translateY(30px);
  opacity: 0;
}



/* f-dialog动画效果 */
.f-dialog {
  will-change: transform, opacity;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 直接应用动画效果 */
.dialog-animation {
  animation: dialog-show 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
  transform-origin: bottom right;
}

/* 关闭动画效果 */
.dialog-animation-close {
  animation: dialog-close 0.3s cubic-bezier(0.6, -0.28, 0.735, 0.045) forwards;
  transform-origin: bottom right;
}

@keyframes dialog-show {
  0% {
    transform: translate(80px, 80px);
    opacity: 0;
  }
  100% {
    transform: translate(0, 0);
    opacity: 1;
  }
}

@keyframes dialog-close {
  0% {
    transform: translate(0, 0);
    opacity: 1;
  }
  100% {
    transform: translate(80px, 80px);
    opacity: 0;
  }
}


.f-payment-btn {
  position: absolute;
  z-index: 200;
  font-size: 22px;
  display: flex;
  justify-content: center;
  align-items: center;
  outline: 0;
  border: 0;
  cursor: pointer;
  will-change: box-shadow,transform;
  background: #3E63DD;
  padding: 0 2em;
  border-radius: 0.3em;
  color: #fff;
  height: 2.6em;
  text-shadow: 0 1px 0 rgb(0 0 0 / 40%);
  transition: box-shadow 0.15s ease, transform 0.15s ease;
}

.f-payment-btn:hover {
  box-shadow: 0px 0.1em 0.2em #3E63DD, 0px 0.4em 0.7em -0.1em  #3E63DD, inset 0px -0.1em 0px #3E63DD;
  transform: translateY(-0.1em);
}

.f-payment-btn:active {
  box-shadow: inset 0px 0.1em 0.6em #3E63DD;
  transform: translateY(0em);
}