.cz_server_list {
    width: 100%;
    height: calc(100vh - 130px);
    box-sizing: border-box;
    padding: 0 15px;
    overflow-y: auto;
}

.cz_server_list::-webkit-scrollbar {
    width: 8px;
    overflow: hidden;
}

.cz_server_list::-webkit-scrollbar-thumb { /*滚动条里面小方块*/
    width: 8px;
    border-radius: 5px;
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
    background: rgba(0, 0, 0, 0.1);
}

.cz_server_list::-webkit-scrollbar-track { /*滚动条里面轨道*/
    -webkit-box-shadow: inset 0 0 5px #eee;
    border-radius: 0;
    background: #fff;
}

.cz_server_list_czk {
    width: 100%;
    margin-bottom: 10px;
    border-radius: 5px;
    cursor: pointer;
}

.cz-bg-img {
    width: 100%;
    height: 100px;
    position: relative;
}

.cz-img {
    width: 100%;
    height: 100%;
    display: inline-block;
}

.cz-main-wrap {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: 15px;
    color: #fff;
}

.cz-card_info {
    margin-bottom: 10px;
}

.cz-capitalbalance {
    margin-bottom: 10px;
    font-size: 14px;
}

.czk_zengsong {
    margin-left: 10px;
}

.principal_gift {
    display: flex;
    justify-content: space-between;
}

.cz_server_list_czk_active {
    width: 90%;
    margin: auto;
    margin-bottom: 10px;
    background-color: #3e63dd;
    border-radius: 5px;
}

.presonal_tel > i {
    position: absolute;
    top: 30px;
    right: 20px;
    font-size: 26px;
    color: #cccccc;
    cursor: pointer;
}

.cz_close {
    position: relative;
}

.el-textarea .el-textarea__inner {
    resize: none;
}

.cz_input {
    width: 50%;
    border: none;
    display: inline-block;
    outline: none;
    font-size: 14px;
}

.vip_pay_cengson_font1 > label {
    color: black;
}

.chioce_paynum_font1 > label {
    color: black;
}

/*  */
.setHeight {
    /*60 80 106*/
    height: calc(100vh - 246px);

    overflow-y: auto;
}

.setHeight::-webkit-scrollbar {
    width: 8px;
    overflow: hidden;
}

.setHeight::-webkit-scrollbar-thumb { /*滚动条里面小方块*/
    width: 8px;
    border-radius: 5px;
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
    background: rgba(0, 0, 0, 0.1);
}

.setHeight::-webkit-scrollbar-track { /*滚动条里面轨道*/
    -webkit-box-shadow: inset 0 0 5px #eee;
    border-radius: 0;
    background: #fff;
}
.offer-wrap {
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    box-sizing: border-box;
    padding: 0 35px;

}

.paymentOffer-list {
    align-self: end;
    box-sizing: border-box;
    padding: 25px 15px;
    width: 240px;
    border: 1px solid rgba(229, 229, 229, 1);
    margin: 10px;
    box-shadow: 0 0 2px 1px rgba(229, 229, 229, 1);
    cursor: pointer;
}

.offer-name, .offer-faceValue {
    margin-bottom: 10px;
}

.checked {
    border: 1px solid #3e63dd;
}
