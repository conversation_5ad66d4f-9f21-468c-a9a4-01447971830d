body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
code,
form,
fieldset,
legend,
textarea,
p,
blockquote,
th,
td,
input,
select,
textarea,
button {
  margin: 0;
  padding: 0;
}

/* 初始化标签在所有浏览器中的margin、padding值 */

fieldset,
img {
  border: 0 none;
}

/* 重置fieldset（表单分组）、图片的边框为0*/

dl,
ul,
ol,
menu,
li {
  list-style: none;
}

/* 重置类表前导符号为onne,menu在HTML5中有效 */

blockquote,
q {
  quotes: none;
}

/* 重置嵌套引用的引号类型 */

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: "";
  content: none;
}

/* 重置嵌套引用*/

input,
select,
textarea,
button {
  vertical-align: middle;
}

/* 重置表单控件垂直居中*/

button {
  border: 0 none;
  background-color: transparent;
  cursor: pointer;
}

/* 重置表单button按钮效果 */

body {
  background: #fff;
}

/* 重置body 页面背景为白色 */

body,
th,
td,
input,
select,
textarea,
button {
  font-size: 16px;
  line-height: 1;
  font-family: "Microsoft YaHei", "SimHei", "SimSun";
  color: #1c2024;
}

/* 重置页面文字属性 */

a {
  color: #1c2024;
  text-decoration: none;
  -webkit-tap-highlight-color: transparent;
}

/* 重置链接a标签 */

a:active,
a:hover {
  text-decoration: none;
}

/* 重置链接a标签的鼠标滑动效果 */

address,
caption,
cite,
code,
dfn,
em,
var {
  font-style: normal;
  font-weight: normal;
}

/* 重置样式标签的样式 */

caption {
  display: none;
}

/* 重置表格标题为隐藏 */

table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  table-layout: fixed;
}

/* 重置table属性 */

img {
  vertical-align: top;
}

/* 图片在当前行内的垂直位置 */

/* 页面设置 */

/* 取消a标签点击后的虚线框 */

a {
  outline: none;
}

a:active {
  star: expression(this.onFocus=this.blur());
}

/* 设置页面文字等在拖动鼠标选中情况下的背景色与文字颜色 */

/*
::selection {color: #fff;background-color: #4C6E78;}
::-moz-selection {color: #fff;background-color: #4C6E78;}
*/

/*清除浮动*/

.clear {
  clear: both;
}

.clear-float:after {
  display: block;
  content: "";
  clear: both;
}
.clear-float,
.clearfix {
  zoom: 1;
}
/*清除浮动--推荐使用*/

.clearfix:before,
.clearfix:after {
  content: "";
  display: table;
}

.clearfix:after {
  clear: both;
}
[v-cloak] {
  display: none;
}

/* 取消半透明灰色 */
html,
body {
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
/* 修改chrome浏览器渲染黄色背景的时间 */
input:-webkit-autofill {
  transition: background-color 5000s ease-in-out 0s;
  color: #000 !important;
}

/*.el-loading-spinner .el-loading-text {*/
/*color: #F97E81;*/
/*}*/
/*.el-loading-spinner .path {*/
/*stroke: #F97E81;*/
/*}*/

/*.el-loading-spinner i {*/
/*color: #F97E81;*/
/*}*/
/*.el-loading-spinner .el-loading-text {*/
/*color: #F97E81;*/
/*margin: 3px 0;*/
/*font-size: 14px;*/
/*}*/

.drag {
  -webkit-app-region: drag;
}

.no-drag {
  -webkit-app-region: no-drag;
}
.my-scrollbar-y .el-scrollbar__wrap {
  overflow-x: hidden !important;
}
[v-cloak] {
  display: none;
}

.o-tag-fuchsia {
  color: #d946ef;
  border: 1px solid #f5d0fe;
  background: #faf5ff;
}

.o-tag-pink {
  color: #ec4899;
  border: 1px solid #fbcfe8;
  background: #fdf2f8;
}

.o-tag-blue {
  color: #3b82f6;
  border: 1px solid #bfdbfe;
  background: #eff6ff;
}

.o-tag-indigo {
  color: #6366f1;
  border: 1px solid #c7d2fe;
  background: #eef2ff;
}

.o-tag-cyan {
  color: #06b6d4;
  border: 1px solid #a5f3fc;
  background: #ecfeff;
}

.o-tag-amber {
  color: #d97706;
  border: 1px solid #fde68a;
  background: #fffbeb;
}

.o-tag-lime {
  color: #4d7c0f;
  border: 1px solid #a3e635;
  background: #f7fee7;
}

.o-tag-gray {
  color: #6b7280;
  border: 1px solid #6b7280;
  background: #f9fafb;
}

.o-tag-orange {
  color: #f97316;
  border: 1px solid #fdba74;
  background: #fff7ed;
}


.el-input--small {
  font-size: 14px !important;
}

.o-numberInput-box{
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 30px;
  background: #fff;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  transition: border-color .2s cubic-bezier(.645,.045,.355,1);
  color: #1c2024;
  overflow: hidden;
}

.o-numberInput-box:hover {
  border: 1px solid #3e63dd;
}

.o-numberInput-box>i{
  background: #F5F7FA;
  cursor: pointer;
  width: 30px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 13px;
}

.o-numberInput-box>.el-icon-minus{
  border-right: 1px solid #DCDFE6;
}
.o-numberInput-box>.el-icon-plus{
  border-left: 1px solid #DCDFE6;
}

.o-numberInput-box .el-input__inner{
  border: none;
}

.o-scrollbar::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.o-scrollbar::-webkit-scrollbar-thumb {
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.o-scrollbar::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}