// 二维码
Vue.directive("qrcode", function (el, binding) {
  el.innerHTML = "";
  var text = binding.value.value;
  if (text) {
    new QRCode(el, {
      text: text,
      width: 88,
      height: 88,
      colorDark: "#000000",
      colorLight: "#ffffff",
      correctLevel: QRCode.CorrectLevel.H,
    });
  }
});

// 条形码
Vue.directive("barcode", function (el, binding) {
  var str = binding.value.value;
  var options = {
    format: "CODE128",
    displayValue: false,
    fontSize: 12,
    height: 50,
    width: 1,
    margin: 0,
  };
  JsBarcode(el, str, options);
});

//店名
Vue.component("app-store", {
  props: {
    storeSet: {
      type: Object,
    },
    store: {
      type: Object,
    },
  },
  /*html*/
  template: `
  <div class="storeTitle">
    <div :style="{fontSize:set.fontSize + 'px'}" style="text-align: center">
        {{storeInfo.storetag}}
    </div>
  </div>
  `,
  data() {
    return {
      set: {},
      storeInfo: {},
    };
  },
  watch: {
    storeSet: {
      handler(n, o) {
        this.set = n;
      },
      deep: true,
      immediate: true,
    },
    store: {
      handler(n, o) {
        this.storeInfo = n;
      },
      deep: true,
      immediate: true,
    },
  },
});

// header
Vue.component("app-header", {
  props: {
    headerSet: {
      type: Object,
    },
    "order-header": {
      type: Object,
    },
  },
  /*html*/
  template: `
  <div
    class="header-wrap"
    style="
      clear: both;
      box-sizing: border-box;
      padding-bottom: 8px;
      margin-bottom: 8px;
      border-bottom: 2px dotted #2b282c;
    "
  >
    <div
      class="headerTitle"
      :style="{fontSize:set.headerfontSize+'px'}"
      style="text-align: center"
    >
      {{set.text}}
    </div>
    <div class="header-main" :style="{fontSize:set.fontSize+'px'}">
      <div class="header-item" v-if="set.ordertype==1">
        <span style="float: left">订单类型:</span>
        <span style="float: right" v-if="!orderInfo.state">店内订单</span>
        <span style="float: right" v-if="orderInfo.state==1">待付款</span>
        <span style="float: right" v-if="orderInfo.state==2">待收货</span>
        <span style="float: right" v-if="orderInfo.state==3">已发货</span>
        <span style="float: right" v-if="orderInfo.state==4">已完成</span>
        <span style="float: right" v-if="orderInfo.state==5">已取消</span>
      </div>
      <div
        class="header-item"
        v-if="set.ordertime==1 && orderInfo.order_number"
        style="clear: both; width: 100%"
      >
        <span style="float: left">订单号:</span>
        <span style="float: right">{{orderInfo.order_number}}</span>
      </div>
      <div
        v-if="set.ordertime==1 && orderInfo.order_time"
        class="header-item"
        style="clear: both; width: 100%"
      >
        <span style="float: left">下单时间:</span>
        <span style="float: right">{{orderInfo.order_time}}</span>
      </div>
      <div
        v-if="set.cashier==1 && orderInfo.cashierInfo && orderInfo.cashierInfo.nickname"
        class="header-item"
        style="clear: both; width: 100%; margin-bottom: 8px"
      >
        <span style="float: left">收银:</span>
        <span style="float: right">{{orderInfo.cashierInfo.nickname}}</span>
      </div>
      <div
        v-if="set.cashier==1 && orderInfo.cashier"
        class="header-item"
        style="clear: both; width: 100%; margin-bottom: 8px"
      >
        <span style="float: left">收银:</span>
        <span style="float: right">{{orderInfo.cashier}}</span>
      </div>
    </div>
  </div>
  `,
  data() {
    return {
      set: {},
      orderInfo: {},
    };
  },
  watch: {
    headerSet: {
      handler(n, o) {
        this.set = n;
      },
      deep: true,
      immediate: true,
    },
    orderHeader: {
      handler(n, o) {
        this.orderInfo = n;
      },
      deep: true,
      immediate: true,
    },
  },
});

// goods
Vue.component("app-goods", {
  props: {
    goodsSet: {
      type: Object,
    },
    goods: {
      type: Array,
    },
    direct: {
      type: null,
    },
  },
  /*html*/
  template: `
  <div :style="{fontSize:set.fontSize +'px'}">
    <div
      class="goodsItme"
      style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
    >
      <div style="text-align: center">消费明细</div>
    </div>
    <div
      class="goods-main"
      style="width: 100%; zoom: 1"
      v-for="(item,index) in shopArr"
      :key="index"
    >
      <div class="goodsIndex" style="float: left; width: 10%; clear: both">
        {{index+1}}
      </div>
      <div class="goodsWrap" style="width: 82%; float: right; margin-right: 10px">
        <div class="goodsItme" style="width: 100%; margin-bottom: 5px; zoom: 1">
          <div class="goodsLeft" style="float: left">
            <span v-if="set.goodsname==1 && item.name">{{item.name}}</span>
            <span v-if="set.sku==1 && item.sku_name">| {{item.sku_name}}</span>
          </div>
          <div
            class="goodsRight"
            style="float: right"
            v-if="set.price==1 && item.price && directType==1"
          >
            ￥{{item.price | filterMoney}}
          </div>
          <div
            class="goodsRight"
            style="float: right"
            v-else-if="set.price==1 && item.price"
          >
            ￥{{item.price}}
          </div>
        </div>
        <div
          class="goodsItme"
          v-if="set.num==1"
          style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
        >
          <span class="goodsLeft" style="float: left">消费数量</span>
          <span class="goodsRight" style="float: right">*{{item.num}}</span>
        </div>
        <div
          class="goodsItme"
          v-if="item.equity_type!=1 && item.equity_type!=4"
          style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
        >
          <span class="goodsLeft" style="float: left" v-if="item.equity_type==3">
            次卡抵扣
          </span>
          <span class="goodsLeft" style="float: left" v-if="item.equity_type==2">
            充值卡折扣
          </span>
          <span
            class="goodsRight"
            style="float: right"
            v-if="item.equity_type!=4"
          >
            -￥{{item.reduceprice | formatMark}}
          </span>
        </div>
        <div
          class="goodsItme"
          v-if="item.equity_type==4"
          style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
        >
          <span class="goodsLeft" style="float: left" v-if="item.equity_type==4">
            优惠金额
          </span>
          <span
            class="goodsRight"
            style="float: right"
            v-if="item.reduceprice.indexOf('-')==-1"
          >
            +￥{{item.reduceprice | formatMark}}
          </span>
          <span
            class="goodsRight"
            style="float: right"
            v-if="item.reduceprice.indexOf('-')!=-1"
          >
            -￥{{item.reduceprice | formatMark}}
          </span>
        </div>
        <div
          class="goodsItme"
          v-show="item.offer && item.offerPrint"
          style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
        >
          <span class="goodsLeft" style="float: left">{{item.offer}}</span>
          <span class="goodsRight" style="float: right">{{item.offerPrint}}</span>
        </div>
        <div
          class="goodsItme"
          v-if="set.allmoney==1 && item.smallTotal"
          style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
        >
          <span class="goodsLeft" style="float: left">小计</span>
          <span class="goodsRight" v-if="directType==1" style="float: right">
            ￥{{item.smallTotal | filterMoney}}
          </span>
          <span class="goodsRight" v-else style="float: right">
            ￥{{item.smallTotal}}
          </span>
        </div>
        <div
          class="goodsItme"
          v-if="set.allmoney==1 && item.Subtotal"
          style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
        >
          <span class="goodsLeft" style="float: left">小计</span>
          <span class="goodsRight" v-if="directType==1" style="float: right">
            ￥{{item.Subtotal | filterMoney}}
          </span>
          <span class="goodsRight" v-else style="float: right">
            ￥{{item.Subtotal}}
          </span>
        </div>
      </div>
    </div>
  </div>
  `,
  data() {
    return {
      set: {},
      shopArr: [],
      directType: 0,
    };
  },
  filters: {
    // 格式化充值金额/100
    filterMoney: function (money) {
      return (money / 100).toFixed(2);
    },
    //格式化正负号
    formatMark: function (money) {
      if (money.indexOf("-") != -1) {
        money = money.split("-")[1];
      }
      return money;
    },
  },
  watch: {
    goodsSet: {
      handler(n, o) {
        this.set = n;
      },
      deep: true,
      immediate: true,
    },
    goods: {
      handler(n, o) {
        this.shopArr = n;
      },
      deep: true,
      immediate: true,
    },
    direct: {
      handler(n, o) {
        this.directType = n;
      },
      deep: true,
      immediate: true,
    },
  },
});

//giftGoods
Vue.component("app-gift", {
  props: {
    goodsSet: {
      type: Object,
    },
    gift: {
      type: Array,
    },
    direct: {
      type: null,
    },
  },
  /*html*/
  template: `
  <div :style="{fontSize:set.fontSize +'px'}">
    <div
      class="goodsItme"
      style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
    >
      <div style="text-align: center" v-if="shopArr && shopArr.length>0">
        赠送明细
      </div>
    </div>
    <div
      class="goods-main"
      style="width: 100%; zoom: 1"
      v-for="(item,index) in shopArr"
      :key="index"
    >
      <div class="goodsIndex" style="float: left; width: 10%; clear: both">
        {{index+1}}
      </div>
      <div class="goodsWrap" style="width: 82%; float: right; margin-right: 10px">
        <div class="goodsItme" style="width: 100%; margin-bottom: 5px; zoom: 1">
          <div class="goodsLeft" style="float: left">
            <span v-if="set.goodsname==1 && item.itemType==1">
              {{item.name}}（服务）
            </span>
            <span v-if="set.goodsname==1 && item.itemType==2 && !item.sku_name">
              {{item.name}}（产品）
            </span>
            <span v-if="set.goodsname==1 && item.itemType==2 && item.sku_name">
              {{item.name}}
            </span>
            <span v-if="set.sku==1 && item.sku_name">
              | {{item.sku_name}}（产品）
            </span>
          </div>
          <div
            class="goodsRight"
            style="float: right"
            v-if="set.price==1 && item.price"
          >
            ￥{{item.price | filterMoney}}
          </div>
        </div>
        <div
          class="goodsItme"
          v-if="set.num==1"
          style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
        >
          <span class="goodsLeft" style="float: left">消费数量</span>
          <span class="goodsRight" style="float: right">*{{item.num}}</span>
        </div>
        <div
          class="goodsItme"
          v-if="item.status>=0"
          style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
        >
          <span class="goodsLeft" style="float: left">赠送状态</span>
          <span class="goodsRight" style="float: right" v-if="item.status==0">
            仅选择
          </span>
          <span class="goodsRight" style="float: right" v-if="item.status==1">
            已赠送
          </span>
          <span class="goodsRight" style="float: right" v-if="item.status==2">
            已退还
          </span>
        </div>
      </div>
    </div>
  </div>
  `,
  data() {
    return {
      set: {},
      shopArr: [],
      directType: 0,
    };
  },
  filters: {
    // 格式化充值金额/100
    filterMoney: function (money) {
      return (money / 100).toFixed(2);
    },
    //格式化正负号
    formatMark: function (money) {
      if (money.indexOf("-") != -1) {
        money = money.split("-")[1];
      }
      return money;
    },
  },
  watch: {
    goodsSet: {
      handler(n, o) {
        this.set = n;
      },
      deep: true,
      immediate: true,
    },
    gift: {
      handler(n, o) {
        this.shopArr = n;
      },
      deep: true,
      immediate: true,
    },
    direct: {
      handler(n, o) {
        this.directType = n;
      },
      deep: true,
      immediate: true,
    },
  },
});

// vip
Vue.component("app-vip", {
  props: {
    vipSet: {
      type: Object,
    },
    member: {
      type: Object,
    },
    orderDetails: {
      type: Object,
    },
    "print-type": {
      type: null,
    },
  },
  /*html*/
  template: `
  <div
  class="vipWrap"
  :style="{fontSize:set.fontSize +'px'}"
  v-if="vip && vip.id"
  style="
    clear: both;
    box-sizing: border-box;
    padding-bottom: 8px;
    margin-bottom: 8px;
    border-bottom: 2px dotted #2b282c;
    border-top: 2px dotted #2b282c;
    font-size: 12px;
    padding-top: 8px;
  "
>
  <div class="vip-item" v-if="set.name==1 && vip && vip.member_name">
    <span style="display: inline-block">会员名称:</span>
    <span>{{vip.member_name}}</span>
  </div>
  <div class="vip-item" v-if="set.cardnum==1">
    <span>会员号码:</span>
    <span>{{vip.phone | filterPhone}}</span>
  </div>
  <template v-if="vip.payCardInfo && vip.payCardInfo.length>0 && set.money==1">
    <template v-for="item in vip.payCardInfo" v-if="item.cardid>0">
      <div class="vip-item">
        <span style="max-width: 50%">{{item.memberCardName}}:</span>
        <span>{{item.residuebalance | filterMoney}}</span>
      </div>
    </template>
  </template>
  <template v-if="cardServiceList?.length>0">
    <div
      style="
        clear: both;
        border-bottom: 2px dotted #2b282c;
        margin-top: 6px;
        margin-bottom: 6px;
      "
    ></div>
    <div style="text-align: center; margin-bottom: 6px">卡项详情</div>
    <div
      class="goods-main"
      style="width: 100%; zoom: 1; padding-bottom: 10px"
      v-for="(item,index) in cardServiceList"
      :key="index"
    >
      <div class="goodsIndex" style="float: left; width: 10%">{{index+1}}</div>
      <div
        class="goodsWrap"
        style="width: 82%; float: right; margin-right: 10px"
      >
        <div
          class="goodsItme"
          style="width: 100%; margin-bottom: 5px; zoom: 1; font-weight: bold"
        >
          {{item.card_info}}
        </div>
        <div
          class="goodsItme"
          style="width: 100%; margin-bottom: 5px; zoom: 1; font-weight: bold"
        >
          {{item.serviceName}}
        </div>
        <div
          v-if="set.showCardTime"
          class="goodsItme"
          style="width: 100%; margin-bottom: 5px; zoom: 1"
        >
          <div class="goodsLeft" style="float: left">有效期：</div>
          <div class="goodsRight" style="float: right">{{item.IndateName}}</div>
        </div>
        <div
          class="goodsItme"
          style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
        >
          <span class="goodsLeft" style="float: left">剩余次数：</span>
          <span class="goodsRight" style="float: right">{{item.maxnum}}</span>
        </div>
        <div
          class="goodsItme"
          style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
        >
          <span class="goodsLeft" style="float: left">总次数：</span>
          <span class="goodsRight" style="float: right">{{item.num}}</span>
        </div>
      </div>
      <div style="clear: both"></div>
    </div>
  </template>
  <div style="clear: both"></div>
</div>

`,
  data() {
    return {
      set: {},
      vip: {},
      orderType: 1,
      // 订单务列表，带哪张卡项id
      orderCardServiceList: [],
      // 被扣卡的卡及服务输出列表
      cardServiceList: [],
    };
  },
  filters: {
    // 格式化充值金额/100
    filterMoney: function (money) {
      return (money / 100).toFixed(2);
    },
    filterPhone: function (phone) {
      return phone.replace(/(\d{3})(\d{4})(\d{4})/, "$1****$3");
    },
  },
  watch: {
    vipSet: {
      handler(n, o) {
        this.set = n;
      },
      deep: true,
      immediate: true,
    },
    member: {
      handler(n, o) {
        this.vip = n;
      },
      deep: true,
      immediate: true,
    },
    printType: {
      handler(n, o) {
        this.orderType = n;
      },
      deep: true,
      immediate: true,
    },
    orderDetails: {
      handler(n, o) {
        console.log('orderDetails watch',n);
        console.log('cardServiceList first', this.cardServiceList);

        // 检查所有必需的数据是否存在
        if(!n || !n.orderInfo || !Array.isArray(n.orderInfo) || !n.paycard_info || !Array.isArray(n.paycard_info)) {
          console.log('Missing required data in orderDetails');
          return;
        }

        let result = [];
        for (let i = 0; i < n.orderInfo.length; i++) {
          for (let j = 0; j < n.paycard_info.length; j++) {
            if (n.orderInfo[i]?.card_id === n.paycard_info[j]?.membercard_id) {
              if (n.paycard_info[j]?.cardDetail && Array.isArray(n.paycard_info[j].cardDetail)) {
                for(let k = 0; k < n.paycard_info[j].cardDetail.length; k++) {
                  if(n.paycard_info[j].cardDetail[k]?.goods_id === n.orderInfo[i]?.goods_id) {
                    // 计算剩余次数
                    const totalNum = n.paycard_info[j].cardDetail[k]?.num || 0;
                    const usedNum = n.paycard_info[j].cardDetail[k]?.usenum || 0;
                    const remainingNum = totalNum - usedNum;

                    result.push({
                      card_info: n.paycard_info[j]?.card_info || '',
                      IndateName: n.paycard_info[j]?.IndateName || '',
                      maxnum: remainingNum,
                      num: totalNum,
                      serviceName: n.orderInfo[i]?.name || '',
                    });
                  }
                }
              }
            }
          }
        }
        this.cardServiceList = result

        console.log('cardServiceList', this.cardServiceList);
      },
      deep: true,
      immediate: true
    },
  },
});

// 收货人信息地址
Vue.component("app-address", {
  props: {
    addressSet: {
      type: Object,
    },
    orderInfo: {
      type: Object,
    },
  },
  /*html*/
  template: `
  <div
    :style="{fontSize:set.fontSize +'px'}"
    v-if="addressInfoFlag && addressInfo && addressInfo.id!=0"
  >
    <div class="area-item" v-if="set.name==1" style="clear: both">
      <span style="float: left">收货人</span>
      <span style="float: right">{{addressInfo.name}}</span>
    </div>
    <div class="area-item" v-if="set.phone==1" style="clear: both">
      <span style="float: left">收货电话</span>
      <span style="float: right" v-if="addressInfo.tel">
        {{addressInfo.tel | filterPhone}}
      </span>
    </div>
    <div class="area-item" v-if="set.address==1" style="clear: both">
      <span style="float: left">收货地址</span>
      <span style="float: right">{{addressInfo.address}}</span>
    </div>
    <div class="area-item" v-if="set.fee==1" style="clear: both">
      <span style="float: left">配送费</span>
      <span style="float: right" v-if="orderDetails.dispatch_fee!='0.00'">
        {{orderDetails.dispatch_fee}}
      </span>
      <span style="float: right" v-if="orderDetails.dispatch_fee=='0.00'">
        无
      </span>
    </div>
    <div class="area-item" v-if="orderDetails.logistics" style="clear: both">
      <span style="float: left">物流公司</span>
      <span style="float: right">{{orderDetails.logistics}}</span>
    </div>
    <div class="area-item" v-if="orderDetails.courier_num" style="clear: both">
      <span style="float: left">物流单号</span>
      <span style="float: right">{{orderDetails.courier_num}}</span>
    </div>
    <div class="area-item" v-if="set.remark==1" style="clear: both">
      <span style="float: left">备注</span>
      <span style="float: right" v-if="orderDetails.remarks!=''">
        {{orderDetails.remarks}}
      </span>
      <span style="float: right" v-if="orderDetails.remarks==''">无</span>
    </div>
  </div>
`,
  data() {
    return {
      set: {},
      orderDetails: {},
      addressInfo: {},
      addressInfoFlag: true,
    };
  },
  filters: {
    // 格式化充值金额/100
    filterMoney: function (money) {
      return (money / 100).toFixed(2);
    },
    filterPhone: function (phone) {
      return phone.replace(/(\d{3})(\d{4})(\d{4})/, "$1****$3");
    },
  },
  watch: {
    addressSet: {
      handler(n, o) {
        this.set = n;
      },
      deep: true,
      immediate: true,
    },
    orderInfo: {
      handler(n, o) {
        this.addressInfo = n.address_info;
        this.orderDetails = n;
        if (JSON.stringify(this.addressInfo) == "{}") {
          this.addressInfoFlag = false;
        }
      },
    },
  },
});

// 线条
Vue.component("app-line", {
  props: {
    lineSet: {
      type: Object,
    },
  },
  /*html*/
  template: `
  <div :style="{fontSize:set.fontSize +'px'}">
    <div class="print-line">
      <table style="width: 100%" border="0">
        <tr>
          <td class="lineItme">
            <span></span>
          </td>
          <td style="text-align: center">{{set.text}}</td>
          <td class="lineItme">
            <span></span>
          </td>
        </tr>
      </table>
    </div>
    <!--<div class="print-line2">-->
    <!--<span class="line-item"></span>-->
    <!--<span>{{set.text}}</span>-->
    <!--</div>-->
  </div>
`,
  data() {
    return {
      set: {},
    };
  },
  watch: {
    lineSet: {
      handler(n, o) {
        this.set = n;
      },
      deep: true,
      immediate: true,
    },
  },
});

// 支付信息
Vue.component("app-footer", {
  props: {
    footerSet: {
      type: Object,
    },
    "order-footer": {
      type: null,
    },
    "pay-type": {
      type: Number,
    },
    "pay-money": {
      type: null,
    },
    "change-money": {
      type: String,
    },
    "print-type": {
      type: null,
    },
  },
  /*html*/
  template: `
  <div :style="{fontSize:set.fontSize +'px'}">
    <div
      class="footer-item"
      v-if="set.paytime==1 && orderInfo.state!=1"
      style="clear: both"
    >
      <span style="float: left">支付时间:</span>
      <span style="float: right">{{orderInfo.collection_time}}</span>
    </div>
    <div
      class="order-date-li"
      v-if="orderInfo.time && orderInfo.state==3"
      style="clear: both"
    >
      <span style="float: left">发货时间</span>
      <span style="float: right">{{orderInfo.time}}</span>
    </div>
    <div
      class="footer-item"
      v-if="set.ordernum==1 && orderInfo.order_number"
      style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
    >
      <span style="float: left">订单号:</span>
      <span style="float: right">{{orderInfo.order_number}}</span>
    </div>
    <div
      class=""
      v-if="set.barcode==1 && orderInfo.order_number"
      style="clear: both; width:100%;"
    >
      <img
        v-barcode="{'value':orderInfo.order_number}"
        style="margin: auto"
      />
    </div>
    <div
      style="clear: both; width: 100%; margin-bottom: 5px;margin-top: 5px; zoom: 1"
      class="footer-item"
    >
      <span style="float: left">应收:</span>
      <span style="float: right; padding-right: 8px">
        ￥{{receivableing | filterMoney}}
      </span>
    </div>
    <div
      style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
      v-if="orderInfo.dismoney!='0.00'"
    >
      <span style="float: left">充值卡折扣</span>
      <span style="float: right; padding-right: 8px" class="order-label2">
        -￥{{orderInfo.dismoney}}
      </span>
    </div>
    <div
      style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
      v-if="orderInfo.deduction!='0.00'"
    >
      <span style="float: left">次卡抵扣</span>
      <span style="float: right; padding-right: 8px" class="order-label2">
        -￥{{orderInfo.deduction}}
      </span>
    </div>
    <div
      style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
      v-if="orderInfo.member_counpon_money"
    >
      <span style="float: left">优惠券</span>
      <span style="float: right; padding-right: 8px" class="order-label2">
        -￥{{orderInfo.member_counpon_money | filterMoney}}
      </span>
    </div>
    <div
      style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
      v-if="orderInfo.manuallys!='0.00'"
    >
      <span style="float: left">优惠金额</span>
      <span
        style="float: right; padding-right: 8px"
        class="order-label2"
        v-if="orderInfo.manually>0"
      >
        -￥{{orderInfo.manuallys}}
      </span>
      <span
        style="float: right; padding-right: 8px"
        class="order-label2"
        v-if="orderInfo.manually<0"
      >
        +￥{{orderInfo.manuallys}}
      </span>
      <span
        style="float: right; padding-right: 8px"
        class="order-label2"
        v-if="orderInfo.manually==0"
      >
        ￥{{orderInfo.manuallys}}
      </span>
    </div>
    <div
      style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
      v-if="orderInfo.small_change_money"
    >
      <span style="float: left">抹零</span>
      <span style="float: right; padding-right: 8px" class="order-label2">
        -￥{{orderInfo.small_change_money | filterMoney}}
      </span>
    </div>
    <div
      style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
      v-if="orderInfo.dispatch_fee!='0.00'"
    >
      <span style="float: left">运费</span>
      <span style="float: right; padding-right: 8px" class="order-label2">
        +￥{{orderInfo.dispatch_fee}}
      </span>
    </div>
    <div
      style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
      class="footer-item"
      v-if="orderType==2 && orderInfo.receivable"
    >
      <span style="float: left">实收:</span>
      <span style="float: right; padding-right: 8px">
        ￥{{orderInfo.receivable}}
      </span>
    </div>
    <!--预付定金-->
    <div
      style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
      class="footer-item"
      v-if="orderInfo.net_receipts"
    >
      <span style="float: left">预付定金:</span>
      <span style="float: right; padding-right: 8px">
        {{orderInfo.netReceipt}}
      </span>
    </div>
    <div
      style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
      class="footer-item"
      v-if="orderType!=2 && set.needpay==1"
    >
      <span style="float: left">实收:</span>
      <span style="float: right; padding-right: 8px">
        ￥{{orderInfo.receivable}}
      </span>
    </div>
    <div
      style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
      class="footer-item"
      v-if="orderInfo.is_debt"
    >
      <span style="float: left">欠款:</span>
      <span style="float: right; padding-right: 8px">
        ￥{{orderInfo.debt_value |
        filterMoney}}({{orderInfo.is_debt==1?"未还":"已还"}})
      </span>
    </div>
    <!--付款情况-->
    <template v-for="payment in orderInfo.payment">
      <div
        style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
        class="footer-item"
      >
        <template v-if="payment.payType==5">
          <!--会员余额支付-->
          <span style="float: left; max-width: 50%">
            {{payment.cardName || '会员余额'}}:
          </span>
        </template>
        <template v-else>
          <span style="float: left; max-width: 50%">
            {{payment.payTypeName}}:
          </span>
        </template>
        <span style="float: right; padding-right: 8px">
          {{payment.trade_amount}}
        </span>
      </div>
    </template>
    <!--现金支付出现-->
    <div
      style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
      class="footer-item"
      v-if="false && isType==2 && payPrice>0"
    >
      <span style="float: left">收款现金:</span>
      <span style="float: right; padding-right: 8px">￥{{payPrice}}</span>
    </div>
    <!--现金支付出现-->
    <div
      style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
      class="footer-item"
      v-if="false && set.smallchange==1 && changePrice>0"
    >
      <span style="float: left">找零金额:</span>
      <span style="float: right; padding-right: 8px">{{changePrice}}</span>
    </div>
    <div
      style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
      class="footer-item"
      v-if="orderInfo.customer_remark || orderInfo.remark"
    >
      <span style="float: left">备注:</span>
      <span style="float: right; padding-right: 8px">
        {{orderInfo.customer_remark || orderInfo.remark}}
      </span>
    </div>
  </div>
`,
  data() {
    return {
      set: {},
      orderInfo: {},
      isType: 0,
      payPrice: 0,
      changePrice: 0,
      curTime: Date.parse(new Date()),
      orderType: 1,
    };
  },
  filters: {
    // 格式化充值金额/100
    filterMoney: function (money) {
      return (money / 100).toFixed(2);
    },
    //格式化正负号
    formatMark: function (money) {
      if (money.indexOf("-") != -1) {
        money = money.split("-")[1];
      }
      return money;
    },
    formatDate: function (value) {
      let date = new Date(value);
      let y = date.getFullYear();
      let MM = date.getMonth() + 1;
      MM = MM < 10 ? "0" + MM : MM;
      let d = date.getDate();
      d = d < 10 ? "0" + d : d;
      let h = date.getHours();
      h = h < 10 ? "0" + h : h;
      let m = date.getMinutes();
      m = m < 10 ? "0" + m : m;
      let s = date.getSeconds();
      s = s < 10 ? "0" + s : s;
      return y + "-" + MM + "-" + d + " " + h + ":" + m + ":" + s;
    },
  },
  computed: {
    receivableing: function () {
      let money = 0;
      if (this.orderInfo.dismoney != "0.00") {
        money = money + Number(this.orderInfo.dismoney) * 100;
      }
      if (this.orderInfo.deduction != "0.00") {
        money = money + Number(this.orderInfo.deduction) * 100;
      }
      if (this.orderInfo.member_counpon_money) {
        money = money + this.orderInfo.member_counpon_money;
      }
      if (this.orderInfo.manuallys != "0.00") {
        money = money + Number(this.orderInfo.manuallys) * 100;
      }
      if (this.orderInfo.small_change_money) {
        money = money + this.orderInfo.small_change_money;
      }
      money = money + Number(this.orderInfo.receivable) * 100;
      if (this.orderInfo.dispatch_fee != "0.00") {
        money = money - Number(this.orderInfo.dispatch_fee) * 100;
      }
      return money;
    },
  },
  watch: {
    footerSet: {
      handler(n, o) {
        this.set = n;
      },
      deep: true,
      immediate: true,
    },
    orderFooter: {
      handler(n, o) {
        this.orderInfo = n;
        this.$forceUpdate();
      },
      deep: true,
      immediate: true,
    },
    payType: {
      handler(n, o) {
        this.isType = n;
      },
      deep: true,
      immediate: true,
    },
    payMoney: {
      handler(n, o) {
        this.payPrice = n;
      },
      deep: true,
      immediate: true,
    },
    changeMoney: {
      handler(n, o) {
        this.changePrice = n;
      },
      deep: true,
      immediate: true,
    },
    printType: {
      handler(n, o) {
        this.orderType = n;
      },
      deep: true,
      immediate: true,
    },
  },
});

// 支付信息
Vue.component("app-footer-debt", {
  props: {
    footerSet: {
      type: Object,
    },
    "order-footer": {
      type: null,
    },
    "pay-type": {
      type: Number,
    },
    "pay-money": {
      type: null,
    },
    "change-money": {
      type: String,
    },
    "print-type": {
      type: null,
    },
  },
  /*html*/
  template: `
  <div :style="{fontSize:set.fontSize +'px'}">
    <div
      class="footer-item"
      v-if="set.paytime==1 && orderInfo.state!=1"
      style="clear: both"
    >
      <span style="float: left">支付时间:</span>
      <span style="float: right">{{orderInfo.collection_time}}</span>
    </div>
    <div
      class="order-date-li"
      v-if="orderInfo.time && orderInfo.state==3"
      style="clear: both"
    >
      <span style="float: left">发货时间</span>
      <span style="float: right">{{orderInfo.time}}</span>
    </div>
    <div
      class="footer-item"
      v-if="set.ordernum==1 && orderInfo.order_number"
      style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
    >
      <span style="float: left">订单号:</span>
      <span style="float: right">{{orderInfo.order_number}}</span>
    </div>
    <div
      class="footer-item"
      v-if="set.barcode==1 && orderInfo.order_number"
      style="clear: both; margin: 0 0 5px 0"
    >
      <img
        v-barcode="{'value':orderInfo.order_number}"
        style="margin-left: -2px"
      />
    </div>
    <div
      style="clear: both; width: 100%; margin-bottom: 5px;margin-top: 5px; zoom: 1"
      v-if="orderInfo.dismoney!='0.00'"
    >
      <span style="float: left">充值卡折扣</span>
      <span style="float: right; padding-right: 8px" class="order-label2">
        -￥{{orderInfo.dismoney}}
      </span>
    </div>
    <div
      style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
      v-if="orderInfo.deduction!='0.00'"
    >
      <span style="float: left">次卡抵扣</span>
      <span style="float: right; padding-right: 8px" class="order-label2">
        -￥{{orderInfo.deduction}}
      </span>
    </div>
    <div
      style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
      v-if="orderInfo.member_counpon_money"
    >
      <span style="float: left">优惠券</span>
      <span style="float: right; padding-right: 8px" class="order-label2">
        -￥{{orderInfo.member_counpon_money | filterMoney}}
      </span>
    </div>
    <div
      style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
      v-if="orderInfo.manuallys!='0.00'"
    >
      <span style="float: left">优惠金额</span>
      <span
        style="float: right; padding-right: 8px"
        class="order-label2"
        v-if="orderInfo.manually>0"
      >
        -￥{{orderInfo.manuallys}}
      </span>
      <span
        style="float: right; padding-right: 8px"
        class="order-label2"
        v-if="orderInfo.manually<0"
      >
        +￥{{orderInfo.manuallys}}
      </span>
      <span
        style="float: right; padding-right: 8px"
        class="order-label2"
        v-if="orderInfo.manually==0"
      >
        ￥{{orderInfo.manuallys}}
      </span>
    </div>
    <div
      style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
      v-if="orderInfo.small_change_money"
    >
      <span style="float: left">抹零</span>
      <span style="float: right; padding-right: 8px" class="order-label2">
        -￥{{orderInfo.small_change_money | filterMoney}}
      </span>
    </div>
    <div
      style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
      class="footer-item"
    >
      <span style="float: left">合计:</span>
      <span style="float: right; padding-right: 8px">
        ￥{{orderInfo.receivable}}
      </span>
    </div>
    <div
      style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
      class="footer-item"
    >
      <span style="float: left">欠款:</span>
      <span style="float: right; padding-right: 8px">
        ￥{{orderInfo.debt_value | filterMoney }}
      </span>
    </div>
    <div
      style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
      class="footer-item"
    >
      <span style="float: left">还款:</span>
      <span style="float: right; padding-right: 8px">
        ￥{{orderInfo.debt_value | filterMoney }}
      </span>
    </div>
  </div>
`,
  data() {
    return {
      set: {},
      orderInfo: {},
      isType: 0,
      payPrice: 0,
      changePrice: 0,
      curTime: Date.parse(new Date()),
      orderType: 1,
    };
  },
  filters: {
    // 格式化充值金额/100
    filterMoney: function (money) {
      return (money / 100).toFixed(2);
    },
    //格式化正负号
    formatMark: function (money) {
      if (money.indexOf("-") != -1) {
        money = money.split("-")[1];
      }
      return money;
    },
    formatDate: function (value) {
      let date = new Date(value);
      let y = date.getFullYear();
      let MM = date.getMonth() + 1;
      MM = MM < 10 ? "0" + MM : MM;
      let d = date.getDate();
      d = d < 10 ? "0" + d : d;
      let h = date.getHours();
      h = h < 10 ? "0" + h : h;
      let m = date.getMinutes();
      m = m < 10 ? "0" + m : m;
      let s = date.getSeconds();
      s = s < 10 ? "0" + s : s;
      return y + "-" + MM + "-" + d + " " + h + ":" + m + ":" + s;
    },
  },
  computed: {
    receivableing: function () {
      let money = 0;
      if (this.orderInfo.dismoney != "0.00") {
        money = money + Number(this.orderInfo.dismoney) * 100;
      }
      if (this.orderInfo.deduction != "0.00") {
        money = money + Number(this.orderInfo.deduction) * 100;
      }
      if (this.orderInfo.member_counpon_money) {
        money = money + this.orderInfo.member_counpon_money;
      }
      if (this.orderInfo.manuallys != "0.00") {
        money = money + Number(this.orderInfo.manuallys) * 100;
      }
      if (this.orderInfo.small_change_money) {
        money = money + this.orderInfo.small_change_money;
      }
      money = money + Number(this.orderInfo.receivable) * 100;
      if (this.orderInfo.dispatch_fee != "0.00") {
        money = money - Number(this.orderInfo.dispatch_fee) * 100;
      }
      return money;
    },
  },
  watch: {
    footerSet: {
      handler(n, o) {
        this.set = n;
      },
      deep: true,
      immediate: true,
    },
    orderFooter: {
      handler(n, o) {
        this.orderInfo = n;
        this.$forceUpdate();
      },
      deep: true,
      immediate: true,
    },
    payType: {
      handler(n, o) {
        this.isType = n;
      },
      deep: true,
      immediate: true,
    },
    payMoney: {
      handler(n, o) {
        this.payPrice = n;
      },
      deep: true,
      immediate: true,
    },
    changeMoney: {
      handler(n, o) {
        this.changePrice = n;
      },
      deep: true,
      immediate: true,
    },
    printType: {
      handler(n, o) {
        this.orderType = n;
      },
      deep: true,
      immediate: true,
    },
  },
});

// 店铺推广
Vue.component("app-info", {
  props: {
    infoSet: {
      type: Object,
    },
  },
  /*html*/
  template: `
  <div class="infoWrap" :style="{fontSize:set.fontSize +'px'}">
    <div
      class="info-text"
      style="clear: both; margin-top: 8px; text-align: center"
    >
      {{set.text}}
    </div>
    <div style="width: 100%; text-align: center; margin: 15px 0">
      <div
        v-qrcode="{'value':set.src}"
        style="width: 88px; height: 88px; text-align: center; margin: auto"
      ></div>
    </div>
  </div>
`,
  data() {
    return {
      set: {},
    };
  },
  watch: {
    infoSet: {
      handler(n, o) {
        this.set = n;
      },
      deep: true,
      immediate: true,
    },
  },
});

// text
Vue.component("app-text", {
  props: {
    textSet: {
      type: Object,
    },
  },
  /*html*/
  template: `
  <div :style="{fontSize:set.fontSize +'px'}">
    <div class="info-text">{{set.text}}</div>
  </div>
`,
  data() {
    return {
      set: {},
    };
  },
  watch: {
    textSet: {
      handler(n, o) {
        this.set = n;
      },
      deep: true,
      immediate: true,
    },
  },
});

//默认打印
Vue.component("app-print", {
  props: {
    store: {
      type: Object,
    },
    orderInfo: {
      type: Object,
    },
    member: {
      type: Object,
    },
    "pay-type": {
      type: Number,
    },
    "pay-money": {
      type: null,
    },
    "change-money": {
      type: String,
    },
    "print-type": {
      type: null,
    },
    goods: {
      type: Array,
    },
    gift: {
      type: Array,
    },
  },
  /*html*/
  template: `
  <div id="prints">
    <div class="storeTitle">
      <div style="fontsize: 20px; margin-bottom: 20px; text-align: center">
        {{storeInfo.storetag}}
      </div>
    </div>

    <div
      class="header-wrap"
      style="
        box-sizing: border-box;
        padding-bottom: 8px;
        margin-bottom: 8px;
        border-bottom: 2px dotted #2b282c;
      "
    >
      <div class="header-main" style="fontsize: 12px">
        <div class="header-item">
          <span style="float: left">订单类型:</span>
          <span style="float: right" v-if="!orderInfoData.state">店内订单</span>
          <span style="float: right" v-if="orderInfoData.state==1">待付款</span>
          <span style="float: right" v-if="orderInfoData.state==2">待收货</span>
          <span style="float: right" v-if="orderInfoData.state==3">已发货</span>
          <span style="float: right" v-if="orderInfoData.state==4">已完成</span>
          <span style="float: right" v-if="orderInfoData.state==5">已取消</span>
        </div>
        <div
          v-if="orderInfo.order_number"
          style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
        >
          <span style="float: left">订单号:</span>
          <span style="float: right">{{orderInfo.order_number}}</span>
        </div>
        <div v-if="orderInfo.order_time" class="header-item" style="clear: both">
          <span style="float: left">下单时间:</span>
          <span style="float: right">{{orderInfoData.order_time}}</span>
        </div>
        <div
          v-if="orderInfo.cashier"
          class="header-item"
          style="clear: both; margin-bottom: 10px"
        >
          <span style="float: left">收银:</span>
          <span style="float: right">{{orderInfo.cashier}}</span>
        </div>
      </div>
    </div>
    <div class="" style="clear: both; fontsize: 12px">
      <!--printInfo.orderInfo   printInfo.orderDetails-->
      <div
        class="goodsItme"
        style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
      >
        <div style="text-align: center">消费明细</div>
      </div>
      <div
        class="goods-main"
        style="width: 100%; zoom: 1"
        v-for="(item,index) in printInfo"
        :key="index"
      >
        <div class="goodsIndex" style="float: left; width: 10%; clear: both">
          {{index+1}}
        </div>
        <div
          class="goodsWrap"
          style="width: 82%; float: right; margin-right: 10px"
        >
          <div class="goodsItme" style="width: 100%; margin-bottom: 5px; zoom: 1">
            <div class="goodsLeft" style="float: left">
              <span v-if="item.name">{{item.name}}</span>
              <span v-if="item.sku_name">| {{item.sku_name}}</span>
            </div>
            <div
              class="goodsRight"
              style="float: right"
              v-if="item.price && directType==1"
            >
              ￥{{item.price | filterMoney}}
            </div>
            <div class="goodsRight" style="float: right" v-else-if="item.price">
              ￥{{item.price}}
            </div>
          </div>
          <div
            class="goodsItme"
            style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
          >
            <span class="goodsLeft" style="float: left">消费数量</span>
            <span class="goodsRight" style="float: right">*{{item.num}}</span>
          </div>
          <div
            class="goodsItme"
            v-if="item.equity_type!=1"
            style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
          >
            <span
              class="goodsLeft"
              style="float: left"
              v-if="item.equity_type==3"
            >
              次卡抵扣
            </span>
            <span
              class="goodsLeft"
              style="float: left"
              v-if="item.equity_type==2"
            >
              充值卡折扣
            </span>
            <span
              class="goodsRight"
              style="float: right"
              v-if="item.equity_type!=4"
            >
              -￥{{item.reduceprice | formatMark}}
            </span>
          </div>
          <div
            class="goodsItme"
            v-if="item.equity_type==4"
            style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
          >
            <span
              class="goodsLeft"
              style="float: left"
              v-if="item.equity_type==4"
            >
              优惠金额
            </span>
            <span
              class="goodsRight"
              style="float: right"
              v-if="item.reduceprice<0"
            >
              +￥{{item.reduceprice | filterMoney}}
            </span>
            <span
              class="goodsRight"
              style="float: right"
              v-if="item.reduceprice>0"
            >
              -￥{{item.reduceprice | filterMoney}}
            </span>
          </div>
          <div
            class="goodsItme"
            v-show="item.offer && item.offerPrint"
            style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
          >
            <span class="goodsLeft" style="float: left">{{item.offer}}</span>
            <span class="goodsRight" style="float: right">
              {{item.offerPrint}}
            </span>
          </div>
          <div
            class="goodsItme"
            v-if="item.smallTotal"
            style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
          >
            <span class="goodsLeft" style="float: left">小计</span>
            <span class="goodsRight" v-if="directType==1" style="float: right">
              ￥{{item.smallTotal | filterMoney}}
            </span>
            <span class="goodsRight" v-else style="float: right">
              ￥{{item.smallTotal}}
            </span>
          </div>
          <div
            class="goodsItme"
            v-if="item.Subtotal"
            style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
          >
            <span class="goodsLeft" style="float: left">小计</span>
            <span class="goodsRight" v-if="directType==1" style="float: right">
              ￥{{item.Subtotal | filterMoney}}
            </span>
            <span class="goodsRight" v-else style="float: right">
              ￥{{item.Subtotal}}
            </span>
          </div>
        </div>
      </div>
    </div>
    <div style="fontsize: 12px">
      <div
        class="goodsItme"
        style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
      >
        <div
          style="text-align: center"
          v-if="presentData && presentData.length>0"
        >
          赠送明细
        </div>
      </div>
      <div
        class="goods-main"
        style="width: 100%; zoom: 1"
        v-for="(item,index) in presentData"
        :key="index"
      >
        <div class="goodsIndex" style="float: left; width: 10%; clear: both">
          {{index+1}}
        </div>
        <div
          class="goodsWrap"
          style="width: 82%; float: right; margin-right: 10px"
        >
          <div class="goodsItme" style="width: 100%; margin-bottom: 5px; zoom: 1">
            <div class="goodsLeft" style="float: left">
              <span v-if="item.itemType==1">{{item.name}}（服务）</span>
              <span v-if="item.itemType==2 && !item.sku_name">
                {{item.name}}（产品）
              </span>
              <span v-if="item.itemType==2 && item.sku_name">{{item.name}}</span>
              <span v-if="item.sku_name">| {{item.sku_name}}（产品）</span>
            </div>
            <div class="goodsRight" style="float: right" v-if="item.price">
              ￥{{item.price | filterMoney}}
            </div>
          </div>
          <div
            class="goodsItme"
            style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
          >
            <span class="goodsLeft" style="float: left">消费数量</span>
            <span class="goodsRight" style="float: right">*{{item.num}}</span>
          </div>
          <div
            class="goodsItme"
            v-if="item.status>=0"
            style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
          >
            <span class="goodsLeft" style="float: left">赠送状态</span>
            <span class="goodsRight" style="float: right" v-if="item.status==0">
              仅选择
            </span>
            <span class="goodsRight" style="float: right" v-if="item.status==1">
              已赠送
            </span>
            <span class="goodsRight" style="float: right" v-if="item.status==2">
              已退还
            </span>
          </div>
        </div>
      </div>
    </div>
    <div
      class="vipWrap"
      style="
        clear: both;
        box-sizing: border-box;
        padding-bottom: 8px;
        margin-bottom: 8px;
        border-bottom: 2px dotted #2b282c;
        fontsize: 12px;
        padding-top: 4px;
        border-top: 2px dotted #2b282c;
      "
      v-if="vip && vip.id!=0"
    >
      <div class="vip-item" style="clear: both" v-if="vip && vip.id!=0">
        <span style="float: left">会员名称:</span>
        <span style="float: right">{{vip.member_name}}</span>
      </div>
      <div
        class="vip-item"
        style="clear: both; margin-bottom: 10px"
        v-if="vip && vip.id!=0"
      >
        <span style="float: left">会员号码:</span>
        <span style="float: right" v-if="vip.phone">
          {{vip.phone | filterPhone}}
        </span>
      </div>
      <div class="vip-item" v-if="orderType==222222">
        <span>会员余额:</span>
        <span>{{vip.balance | filterMoney}}</span>
      </div>
      <div style="display: none" v-if="orderType==22222222222" class="vip-item">
        <span>会员积分:</span>
        <span>19966688899</span>
      </div>
    </div>
    <div
      style="clear: both; font-size: 12px"
      v-if="addressInfoFlag && addressInfo && addressInfo.id!=0"
    >
      <div class="area-item" style="clear: both">
        <span style="float: left">收货人</span>
        <span style="float: right">{{addressInfo.name}}</span>
      </div>
      <div class="area-item" style="clear: both">
        <span style="float: left">收货电话</span>
        <span style="float: right" v-if="addressInfo.tel">
          {{addressInfo.tel | filterPhone}}
        </span>
      </div>
      <div class="area-item" style="clear: both">
        <span style="float: left">收货地址</span>
        <span style="float: right">{{addressInfo.address}}</span>
      </div>
      <div class="area-item" style="clear: both">
        <span style="float: left">运费</span>
        <span style="float: right" v-if="orderInfoData.dispatch_fee!='0.00'">
          {{orderInfoData.dispatch_fee}}
        </span>
        <span style="float: right" v-if="orderInfoData.dispatch_fee=='0.00'">
          无
        </span>
      </div>
      <div class="area-item" v-if="orderInfoData.logistics" style="clear: both">
        <span style="float: left">物流公司</span>
        <span style="float: right">{{orderInfoData.logistics}}</span>
      </div>
      <div class="area-item" v-if="orderInfoData.courier_num" style="clear: both">
        <span style="float: left">物流单号</span>
        <span style="float: right">{{orderInfoData.courier_num}}</span>
      </div>
      <div class="area-item" style="clear: both">
        <span style="float: left">备注</span>
        <span style="float: right" v-if="orderInfoData.remarks!=''">
          {{orderInfoData.remarks}}
        </span>
        <span style="float: right" v-if="orderInfoData.remarks==''">无</span>
      </div>
    </div>

    <div style="clear: both; fontsize: 12px">
      <div class="footer-item" v-if="orderInfoData.state!=1">
        <span style="float: left">支付时间:</span>
        <span style="float: right">{{orderInfoData.collection_time}}</span>
      </div>
      <div
        class="order-date-li"
        v-if="orderInfoData.time && orderInfoData.state==3"
        style="clear: both"
      >
        <span style="float: left">发货时间</span>
        <span style="float: right">{{orderInfoData.time}}</span>
      </div>
      <div
        class="footer-item"
        v-if="orderInfoData.order_number"
        style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
      >
        <span style="float: left">订单号:</span>
        <span style="float: right">{{orderInfoData.order_number}}</span>
      </div>
      <div
        class="footer-item"
        v-if="orderInfoData.order_number"
        style="margin: 0 0 5px 0"
      >
        <img v-barcode="{'value':orderInfoData.order_number}" />
      </div>
      <div
        style="clear: both; width: 100%; margin: 0 20px 5px 0; zoom: 1"
        class="footer-item"
      >
        <span style="float: left">应收:</span>
        <span style="float: right; padding-right: 8px">
          ￥{{receivableing | filterMoney}}
        </span>
      </div>
      <div
        style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
        v-if="orderInfoData.dismoney!='0.00'"
      >
        <span style="float: left">充值卡折扣</span>
        <span style="float: right; padding-right: 8px" class="order-label2">
          -￥{{orderInfoData.dismoney}}
        </span>
      </div>
      <div
        style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
        v-if="orderInfoData.deduction!='0.00'"
      >
        <span style="float: left">次卡抵扣</span>
        <span style="float: right; padding-right: 8px" class="order-label2">
          -￥{{orderInfoData.deduction}}
        </span>
      </div>
      <div
        style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
        v-if="orderInfoData.member_counpon_money"
      >
        <span style="float: left">优惠券</span>
        <span style="float: right; padding-right: 8px" class="order-label2">
          -￥{{orderInfoData.member_counpon_money | filterMoney}}
        </span>
      </div>
      <div
        style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
        v-if="orderInfoData.manuallys!='0.00'"
      >
        <span style="float: left">优惠金额</span>
        <span
          style="float: right; padding-right: 8px"
          class="order-label2"
          v-if="orderInfoData.manually>0"
        >
          -￥{{orderInfoData.manuallys}}
        </span>
        <span
          style="float: right; padding-right: 8px"
          class="order-label2"
          v-if="orderInfoData.manually<0"
        >
          +￥{{orderInfoData.manuallys}}
        </span>
        <span
          style="float: right; padding-right: 8px"
          class="order-label2"
          v-if="orderInfoData.manually==0"
        >
          ￥{{orderInfoData.manuallys}}
        </span>
      </div>
      <div
        style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
        v-if="orderInfoData.small_change_money"
      >
        <span style="float: left">抹零</span>
        <span style="float: right; padding-right: 8px" class="order-label2">
          -￥{{orderInfoData.small_change_money | filterMoney}}
        </span>
      </div>
      <div
        style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
        v-if="orderInfoData.dispatch_fee!='0.00'"
      >
        <span style="float: left">运费</span>
        <span style="float: right; padding-right: 8px" class="order-label2">
          +￥{{orderInfoData.dispatch_fee}}
        </span>
      </div>
      <div
        style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
        class="footer-item"
        v-if="orderType==2 && orderInfoData.receivable"
      >
        <span style="float: left">实收:</span>
        <span style="float: right; padding-right: 8px">
          ￥{{orderInfoData.receivable}}
        </span>
      </div>
      <!--<div class="footer-item" >-->
      <!--<span>预付定金:</span>-->
      <!--<span>预付定金</span>-->
      <!--</div>-->
      <div
        style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
        class="footer-item"
        v-if="orderType!=2"
      >
        <span style="float: left">实收:</span>
        <span style="float: right; padding-right: 8px">
          ￥{{orderInfoData.receivable}}
        </span>
      </div>
      <!--现金支付出现-->
      <div
        style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
        class="footer-item"
        v-if="false && isType==2 && payPrice>0"
      >
        <span style="float: left">收款现金:</span>
        <span style="float: right; padding-right: 8px">￥{{payPrice}}</span>
      </div>
      <div
        style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
        class="footer-item"
        v-if="false && orderType!=2"
      >
        <span style="float: left">支付方式:</span>
        <span style="float: right; padding-right: 8px" v-if="isType==0">
          会员支付
        </span>
        <span style="float: right; padding-right: 8px" v-if="isType==1">
          在线支付
        </span>
        <span style="float: right; padding-right: 8px" v-if="isType==2">
          现金
        </span>
        <!--<span v-if="orderType==2 && orderInfoData.payment_method">{{orderInfoData.payment_method}}</span>-->
      </div>
      <div class="footer-item" style="display: none">
        <span>抵扣|折扣|改价:</span>
        <span>抵扣|折扣|改价</span>
      </div>
      <!--现金支付出现-->
      <div
        style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
        class="footer-item"
        v-if="false && changePrice>0"
      >
        <span style="float: left">找零金额:</span>
        <span style="float: right; padding-right: 8px">{{changePrice}}</span>
      </div>
    </div>
    <p>谢谢惠顾，欢迎再次光临</p>
  </div>
  `,

  data() {
    return {
      storeInfo: {},
      printInfo: {},
      vip: {},
      isType: 0,
      payPrice: 0,
      changePrice: 0,
      orderType: 1,
      directType: 0,
      presentData: [],
      orderInfoData: {},
      addressInfo: {},
      addressInfoFlag: true,
    };
  },

  computed: {
    // goods: function () {
    //     if (this.orderType == 2) {
    //
    //     }
    // }
    receivableing: function () {
      let money = 0;
      if (this.orderInfoData.dismoney != "0.00") {
        money = money + Number(this.orderInfoData.dismoney) * 100;
      }
      if (this.orderInfoData.deduction != "0.00") {
        money = money + Number(this.orderInfoData.deduction) * 100;
      }
      if (this.orderInfoData.member_counpon_money) {
        money = money + this.orderInfoData.member_counpon_money;
      }
      if (this.orderInfoData.manuallys != "0.00") {
        money = money + Number(this.orderInfoData.manuallys) * 100;
      }
      if (this.orderInfoData.small_change_money) {
        money = money + this.orderInfoData.small_change_money;
      }
      money = money + Number(this.orderInfoData.receivable) * 100;
      if (this.orderInfo.dispatch_fee != "0.00") {
        money = money - Number(this.orderInfo.dispatch_fee) * 100;
      }
      return money;
    },
  },

  filters: {
    // 格式化充值金额/100
    filterMoney: function (money) {
      return (money / 100).toFixed(2);
    },
    formatDate: function (value) {
      let date = new Date(value);
      let y = date.getFullYear();
      let MM = date.getMonth() + 1;
      MM = MM < 10 ? "0" + MM : MM;
      let d = date.getDate();
      d = d < 10 ? "0" + d : d;
      let h = date.getHours();
      h = h < 10 ? "0" + h : h;
      let m = date.getMinutes();
      m = m < 10 ? "0" + m : m;
      let s = date.getSeconds();
      s = s < 10 ? "0" + s : s;
      return y + "-" + MM + "-" + d + " " + h + ":" + m + ":" + s;
    },
    //格式化手机号，隐藏中间后四位
    filterPhone: function (phone) {
      return phone.replace(/(\d{3})(\d{4})(\d{4})/, "$1****$3");
    },
    //格式化正负号
    formatMark: function (money) {
      money = String(money);
      if (money.indexOf("-") != -1) {
        money = money.split("-")[1];
      }
      return money;
    },
  },
  watch: {
    store: {
      handler(n) {
        this.storeInfo = n;
      },
      deep: true,
      immediate: true,
    },
    orderInfo: {
      handler(n) {
        if (this.orderType == 2) {
          this.printInfo = n.orderInfo;
        } else {
          this.printInfo = n.orderDetails;
        }
        this.orderInfoData = n;
        this.addressInfo = n.address_info;
        if (JSON.stringify(this.addressInfo) == "{}") {
          this.addressInfoFlag = false;
        }
      },
      deep: true,
      immediate: true,
    },
    member: {
      handler(n) {
        this.vip = n;
      },
      deep: true,
      immediate: true,
    },
    payType: {
      handler(n, o) {
        this.isType = n;
      },
      deep: true,
      immediate: true,
    },
    payMoney: {
      handler(n, o) {
        this.payPrice = n;
      },
      deep: true,
      immediate: true,
    },
    changeMoney: {
      handler(n, o) {
        this.changePrice = n;
      },
      deep: true,
      immediate: true,
    },
    printType: {
      handler(n, o) {
        this.orderType = n;
      },
      deep: true,
      immediate: true,
    },
    direct: {
      handler(n, o) {
        this.directType = n;
      },
      deep: true,
      immediate: true,
    },
    gift: {
      handler(n, o) {
        this.presentData = n;
      },
      deep: true,
      immediate: true,
    },
  },
});

//默认打印 (还款)
Vue.component("app-print-debt", {
  props: {
    store: {
      type: Object,
    },
    orderInfo: {
      type: Object,
    },
    member: {
      type: Object,
    },
    "pay-type": {
      type: Number,
    },
    "pay-money": {
      type: null,
    },
    "change-money": {
      type: String,
    },
    "print-type": {
      type: null,
    },
    goods: {
      type: Array,
    },
    gift: {
      type: Array,
    },
  },
  /*html*/
  template: `
  <div id="prints">
    <div class="storeTitle">
      <div style="fontsize: 20px; margin-bottom: 20px; text-align: center">
        {{storeInfo.storetag}}
      </div>
    </div>

    <div
      class="header-wrap"
      style="
        box-sizing: border-box;
        padding-bottom: 8px;
        margin-bottom: 8px;
        border-bottom: 2px dotted #2b282c;
      "
    >
      <div class="header-main" style="fontsize: 12px">
        <div class="header-item">
          <span style="float: left">订单类型:</span>
          <span style="float: right" v-if="!orderInfoData.state">店内订单</span>
          <span style="float: right" v-if="orderInfoData.state==1">待付款</span>
          <span style="float: right" v-if="orderInfoData.state==2">待收货</span>
          <span style="float: right" v-if="orderInfoData.state==3">已发货</span>
          <span style="float: right" v-if="orderInfoData.state==4">已完成</span>
          <span style="float: right" v-if="orderInfoData.state==5">已取消</span>
        </div>
        <div
          v-if="orderInfo.order_number"
          style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
        >
          <span style="float: left">订单号:</span>
          <span style="float: right">{{orderInfo.order_number}}</span>
        </div>
        <div v-if="orderInfo.order_time" class="header-item" style="clear: both">
          <span style="float: left">下单时间:</span>
          <span style="float: right">{{orderInfoData.order_time}}</span>
        </div>
        <div
          v-if="orderInfo.cashier"
          class="header-item"
          style="clear: both; margin-bottom: 10px"
        >
          <span style="float: left">收银:</span>
          <span style="float: right">{{orderInfo.cashier}}</span>
        </div>
      </div>
    </div>
    <div class="" style="clear: both; fontsize: 12px">
      <!--printInfo.orderInfo   printInfo.orderDetails-->
      <div
        class="goodsItme"
        style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
      >
        <div style="text-align: center">消费明细</div>
      </div>
      <div
        class="goods-main"
        style="width: 100%; zoom: 1"
        v-for="(item,index) in printInfo"
        :key="index"
      >
        <div class="goodsIndex" style="float: left; width: 10%; clear: both">
          {{index+1}}
        </div>
        <div
          class="goodsWrap"
          style="width: 82%; float: right; margin-right: 10px"
        >
          <div class="goodsItme" style="width: 100%; margin-bottom: 5px; zoom: 1">
            <div class="goodsLeft" style="float: left">
              <span v-if="item.name">{{item.name}}</span>
              <span v-if="item.sku_name">| {{item.sku_name}}</span>
            </div>
            <div
              class="goodsRight"
              style="float: right"
              v-if="item.price && directType==1"
            >
              ￥{{item.price | filterMoney}}
            </div>
            <div class="goodsRight" style="float: right" v-else-if="item.price">
              ￥{{item.price}}
            </div>
          </div>
          <div
            class="goodsItme"
            style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
          >
            <span class="goodsLeft" style="float: left">消费数量</span>
            <span class="goodsRight" style="float: right">*{{item.num}}</span>
          </div>
          <div
            class="goodsItme"
            v-if="item.equity_type!=1"
            style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
          >
            <span
              class="goodsLeft"
              style="float: left"
              v-if="item.equity_type==3"
            >
              次卡抵扣
            </span>
            <span
              class="goodsLeft"
              style="float: left"
              v-if="item.equity_type==2"
            >
              充值卡折扣
            </span>
            <span
              class="goodsRight"
              style="float: right"
              v-if="item.equity_type!=4"
            >
              -￥{{item.reduceprice | formatMark}}
            </span>
          </div>
          <div
            class="goodsItme"
            v-if="item.equity_type==4"
            style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
          >
            <span
              class="goodsLeft"
              style="float: left"
              v-if="item.equity_type==4"
            >
              优惠金额
            </span>
            <span
              class="goodsRight"
              style="float: right"
              v-if="item.reduceprice<0"
            >
              +￥{{item.reduceprice | filterMoney}}
            </span>
            <span
              class="goodsRight"
              style="float: right"
              v-if="item.reduceprice>0"
            >
              -￥{{item.reduceprice | filterMoney}}
            </span>
          </div>
          <div
            class="goodsItme"
            v-show="item.offer && item.offerPrint"
            style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
          >
            <span class="goodsLeft" style="float: left">{{item.offer}}</span>
            <span class="goodsRight" style="float: right">
              {{item.offerPrint}}
            </span>
          </div>
          <div
            class="goodsItme"
            v-if="item.smallTotal"
            style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
          >
            <span class="goodsLeft" style="float: left">小计</span>
            <span class="goodsRight" v-if="directType==1" style="float: right">
              ￥{{item.smallTotal | filterMoney}}
            </span>
            <span class="goodsRight" v-else style="float: right">
              ￥{{item.smallTotal}}
            </span>
          </div>
          <div
            class="goodsItme"
            v-if="item.Subtotal"
            style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
          >
            <span class="goodsLeft" style="float: left">小计</span>
            <span class="goodsRight" v-if="directType==1" style="float: right">
              ￥{{item.Subtotal | filterMoney}}
            </span>
            <span class="goodsRight" v-else style="float: right">
              ￥{{item.Subtotal}}
            </span>
          </div>
        </div>
      </div>
    </div>
    <div style="fontsize: 12px">
      <div
        class="goodsItme"
        style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
      >
        <div
          style="text-align: center"
          v-if="presentData && presentData.length>0"
        >
          赠送明细
        </div>
      </div>
      <div
        class="goods-main"
        style="width: 100%; zoom: 1"
        v-for="(item,index) in presentData"
        :key="index"
      >
        <div class="goodsIndex" style="float: left; width: 10%; clear: both">
          {{index+1}}
        </div>
        <div
          class="goodsWrap"
          style="width: 82%; float: right; margin-right: 10px"
        >
          <div class="goodsItme" style="width: 100%; margin-bottom: 5px; zoom: 1">
            <div class="goodsLeft" style="float: left">
              <span v-if="item.itemType==1">{{item.name}}（服务）</span>
              <span v-if="item.itemType==2 && !item.sku_name">
                {{item.name}}（产品）
              </span>
              <span v-if="item.itemType==2 && item.sku_name">{{item.name}}</span>
              <span v-if="item.sku_name">| {{item.sku_name}}（产品）</span>
            </div>
            <div class="goodsRight" style="float: right" v-if="item.price">
              ￥{{item.price | filterMoney}}
            </div>
          </div>
          <div
            class="goodsItme"
            style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
          >
            <span class="goodsLeft" style="float: left">消费数量</span>
            <span class="goodsRight" style="float: right">*{{item.num}}</span>
          </div>
          <div
            class="goodsItme"
            v-if="item.status>=0"
            style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
          >
            <span class="goodsLeft" style="float: left">赠送状态</span>
            <span class="goodsRight" style="float: right" v-if="item.status==0">
              仅选择
            </span>
            <span class="goodsRight" style="float: right" v-if="item.status==1">
              已赠送
            </span>
            <span class="goodsRight" style="float: right" v-if="item.status==2">
              已退还
            </span>
          </div>
        </div>
      </div>
    </div>
    <div style="clear: both; fontsize: 12px">
      <div class="footer-item" v-if="orderInfoData.state!=1">
        <span style="float: left">支付时间:</span>
        <span style="float: right">{{orderInfoData.collection_time}}</span>
      </div>
      <div
        class="footer-item"
        v-if="orderInfoData.order_number"
        style="clear: both; width: 100%; margin-bottom: 5px; zoom: 1"
      >
        <span style="float: left">订单号:</span>
        <span style="float: right">{{orderInfoData.order_number}}</span>
      </div>
      <div
        class="footer-item"
        v-if="orderInfoData.order_number"
        style="margin: 0 0 5px 0"
      >
        <img v-barcode="{'value':orderInfoData.order_number}" />
      </div>
      <div style="clear: both; width: 100%; margin-bottom: 10px; zoom: 1">
        <span style="float: left">合计</span>
        <span style="float: right; padding-right: 8px" class="order-label2">
          ￥{{orderInfoData.receivable | filterMoney}}
        </span>
      </div>
      <div
        style="clear: both; width: 100%; margin-bottom: 10px; zoom: 1"
        v-if="payPrice>0"
      >
        <span style="float: left">欠款</span>
        <span style="float: right; padding-right: 8px" class="order-label2">
          ￥{{payPrice}}
        </span>
      </div>
      <div
        style="clear: both; width: 100%; margin-bottom: 10px; zoom: 1"
        v-if="payPrice>0"
      >
        <span style="float: left">还款</span>
        <span style="float: right; padding-right: 8px" class="order-label2">
          ￥{{payPrice}}
        </span>
      </div>
      <div style="clear: both; width: 100%; margin-bottom: 10px; zoom: 1"></div>
    </div>
    <p>谢谢惠顾，欢迎再次光临</p>
  </div>
  `,

  data() {
    return {
      storeInfo: {},
      printInfo: {},
      vip: {},
      isType: 0,
      payPrice: 0,
      changePrice: 0,
      orderType: 1,
      directType: 0,
      presentData: [],
      orderInfoData: {},
      addressInfo: {},
      addressInfoFlag: true,
    };
  },

  computed: {
    // goods: function () {
    //     if (this.orderType == 2) {
    //
    //     }
    // }
    receivableing: function () {
      let money = 0;
      if (this.orderInfoData.dismoney != "0.00") {
        money = money + Number(this.orderInfoData.dismoney) * 100;
      }
      if (this.orderInfoData.deduction != "0.00") {
        money = money + Number(this.orderInfoData.deduction) * 100;
      }
      if (this.orderInfoData.member_counpon_money) {
        money = money + this.orderInfoData.member_counpon_money;
      }
      if (this.orderInfoData.manuallys != "0.00") {
        money = money + Number(this.orderInfoData.manuallys) * 100;
      }
      if (this.orderInfoData.small_change_money) {
        money = money + this.orderInfoData.small_change_money;
      }
      money = money + Number(this.orderInfoData.receivable) * 100;
      if (this.orderInfo.dispatch_fee != "0.00") {
        money = money - Number(this.orderInfo.dispatch_fee) * 100;
      }
      return money;
    },
  },

  filters: {
    // 格式化充值金额/100
    filterMoney: function (money) {
      return (money / 100).toFixed(2);
    },
    formatDate: function (value) {
      let date = new Date(value);
      let y = date.getFullYear();
      let MM = date.getMonth() + 1;
      MM = MM < 10 ? "0" + MM : MM;
      let d = date.getDate();
      d = d < 10 ? "0" + d : d;
      let h = date.getHours();
      h = h < 10 ? "0" + h : h;
      let m = date.getMinutes();
      m = m < 10 ? "0" + m : m;
      let s = date.getSeconds();
      s = s < 10 ? "0" + s : s;
      return y + "-" + MM + "-" + d + " " + h + ":" + m + ":" + s;
    },
    //格式化手机号，隐藏中间后四位
    filterPhone: function (phone) {
      return phone.replace(/(\d{3})(\d{4})(\d{4})/, "$1****$3");
    },
    //格式化正负号
    formatMark: function (money) {
      money = String(money);
      if (money.indexOf("-") != -1) {
        money = money.split("-")[1];
      }
      return money;
    },
  },
  watch: {
    store: {
      handler(n) {
        this.storeInfo = n;
      },
      deep: true,
      immediate: true,
    },
    orderInfo: {
      handler(n) {
        if (this.orderType == 2) {
          this.printInfo = n.orderInfo;
        } else {
          this.printInfo = n.orderDetails;
        }
        this.orderInfoData = n;
        this.addressInfo = n.address_info;
        if (JSON.stringify(this.addressInfo) == "{}") {
          this.addressInfoFlag = false;
        }
      },
      deep: true,
      immediate: true,
    },
    member: {
      handler(n) {
        this.vip = n;
      },
      deep: true,
      immediate: true,
    },
    payType: {
      handler(n, o) {
        this.isType = n;
      },
      deep: true,
      immediate: true,
    },
    payMoney: {
      handler(n, o) {
        this.payPrice = n;
      },
      deep: true,
      immediate: true,
    },
    changeMoney: {
      handler(n, o) {
        this.changePrice = n;
      },
      deep: true,
      immediate: true,
    },
    printType: {
      handler(n, o) {
        this.orderType = n;
      },
      deep: true,
      immediate: true,
    },
    direct: {
      handler(n, o) {
        this.directType = n;
      },
      deep: true,
      immediate: true,
    },
    gift: {
      handler(n, o) {
        this.presentData = n;
      },
      deep: true,
      immediate: true,
    },
  },
});

// 打印耗材
Vue.component("app-cost", {
  props: {
    store: {
      type: Object,
    },
    cost: {
      type: Object,
    },
  },
  /*html*/
  template: `
  <div id="prints" style="clear: both">
    <div class="storeTitle" style="clear: both">
      <div style="fontsize: 20px; margin-bottom: 20px; text-align: center">
        {{storeInfo.storetag}}
      </div>
      <div style="fontsize: 16px; margin-bottom: 20px; text-align: center">
        耗材小票
      </div>
    </div>
    <div
      class="footer-item"
      v-if="vip && vip.member_name"
      style="clear: both; fontsize: 12px"
    >
      <span style="float: left">会员名称:</span>
      <span style="float: right">{{vip.member_name}}</span>
    </div>
    <div
      class="footer-item"
      v-if="vip && vip.phone"
      style="clear: both; fontsize: 12px"
    >
      <span style="float: left">会员号码:</span>
      <span style="float: right">{{vip.phone | filterPhone}}</span>
    </div>
    <div
      class="footer-item"
      v-if="order_number"
      style="
        clear: both;
        width: 100%;
        margin-bottom: 8px;
        zoom: 1;
        fontsize: 12px;
      "
    >
      <span style="float: left">订单号:</span>
      <span style="float: right">{{order_number}}</span>
    </div>
    <div
      class="footer-item"
      v-if="order_number"
      style="clear: both; margin: 0 0 10px 0"
    >
      <img v-barcode="{'value':order_number}" />
    </div>
    <div
      v-for="item in costData"
      style="
        clear: both;
        margin: 8px 0;
        border-bottom: 2px dotted #2b282c;
        fontsize: 12px;
      "
    >
      <div
        class="footer-item"
        v-if="item.service_name"
        style="clear: both; width: 100%; margin-bottom: 8px; zoom: 1"
      >
        <span style="float: left">{{item.service_name}}</span>
        <span style="float: right"></span>
      </div>
      <div
        v-for="item1 in item.consumablesData"
        style="clear: both; width: 94%; margin-left: 12px"
      >
        <div
          class="footer-item"
          v-if="item1.productinfo && item1.productinfo.product_name && item1.productinfo.unit && item1.num"
          style="clear: both; width: 100%; margin-bottom: 8px; zoom: 1"
        >
          <span style="float: left" v-if="item1.consumableStatus==1">
            {{item1.productinfo.product_name}} | {{item1.productinfo.unit}}
          </span>
          <span style="float: left" v-if="item1.consumableStatus==2">
            {{item1.productinfo.product_name}} | {{item1.productinfo.unit}}
          </span>
          <span style="float: right" v-if="item1.consumableStatus==1">
            *{{item1.num}}
          </span>
          <span style="float: right" v-if="item1.consumableStatus==2">
            *{{item1.num}}(本店无)
          </span>
        </div>
      </div>
      <div style="clear: both; margin-bottom: 8px">
        <span
          style="float: left"
          v-if="item.consumablesData && item.consumablesData.length==0"
        >
          未关联耗材
        </span>
      </div>
      <div style="clear: both"></div>
    </div>
    <div
      v-for="item in costArr"
      style="
        clear: both;
        margin: 8px 0;
        border-bottom: 2px dotted #2b282c;
        fontsize: 12px;
      "
    >
      <div
        class="footer-item"
        v-if="item.service_name"
        style="clear: both; width: 100%; margin-bottom: 8px; zoom: 1"
      >
        <span style="float: left">{{item.service_name}}</span>
        <span style="float: right">已出库</span>
      </div>
      <div
        v-for="item1 in item.consumablesData"
        style="clear: both; width: 94%; margin-left: 12px"
      >
        <div
          class="footer-item"
          v-if="item1.productinfo && item1.productinfo.product_name && item1.productinfo.unit && item1.num"
          style="clear: both; width: 100%; margin-bottom: 8px; zoom: 1"
        >
          <span style="float: left" v-if="item1.type==2">(退)</span>
          <span style="float: left" v-if="item1.consumableStatus==1">
            {{item1.productinfo.product_name}} | {{item1.productinfo.unit}}
          </span>
          <span style="float: left" v-if="item1.consumableStatus==2">
            {{item1.productinfo.product_name}} | {{item1.productinfo.unit}}
          </span>
          <span style="float: right" v-if="item1.consumableStatus==1">
            *{{item1.num}}
          </span>
          <span style="float: right" v-if="item1.consumableStatus==2">
            *{{item1.num}}(本店无)
          </span>
        </div>
      </div>
      <div style="clear: both; margin-bottom: 8px; float: right">
        {{item.receiptorName}}
      </div>
      <div style="clear: both"></div>
    </div>
    <div
      style="clear: both; margin-bottom: 8px"
      v-if="costData && costData.length==0 && costArr && costArr.length==0"
    >
      订单内无服务
    </div>
    <div style="clear: both; text-align: center; pdding-bottom: 10px">
      凭此小票领取服务耗材
    </div>
  </div>
`,
  data() {
    return {
      storeInfo: {},
      costData: {}, //未出库的耗材
      costArr: [], //已出库的耗材
      order_number: 0,
      vip: {},
    };
  },
  watch: {
    cost: {
      handler(n, o) {
        this.costData = n.data;

        this.costArr = n.hasOutData;
        this.order_number = n.orderNo;
        this.vip = n.buyer;
      },
      deep: true,
      immediate: true,
    },
    store: {
      handler(n) {
        this.storeInfo = n;
      },
      deep: true,
      immediate: true,
    },
  },
  filters: {
    filterPhone: function (phone) {
      return phone.replace(/(\d{3})(\d{4})(\d{4})/, "$1****$3");
    },
  },
});

var LODOP; //声明为全局变量
var LODOPbol = true;

function Preview1() {
  if (!LODOPbol) {
    return;
  }
  LODOP = getLodop();
  //console.log(LODOP);
  if (LODOP) {
    LODOP.SET_PREVIEW_WINDOW(0, 1, 0, 800, 600, "");
    LODOP.SET_SHOW_MODE("PREVIEW_NO_MINIMIZE", true); //预览窗口禁止最小化，并始终最前
    LODOP.SET_PRINT_MODE("AUTO_CLOSE_PREWINDOW", 1);
    AddPrintContent();
    LODOP.PRINT();
  } else {
    app && typeof app.noPrint === "function" && app.noPrint();
  }
}

function AddPrintContent(strCode, strName) {
  var strStyleCSS = "",
    styles = document.querySelectorAll("style,link");
  for (var i = 0; i < styles.length; i++) {
    strStyleCSS += styles[i].outerHTML;
  }
  var str = document.getElementById("prints").innerHTML;
  LODOP.SET_PREVIEW_WINDOW(1, 0, 0, 0, 0, "");
  LODOP.SET_PRINT_MODE("PRINT_PAGE_PERCENT", "100%");
  LODOP.ADD_PRINT_HTM(
    0,
    0,
    "100%",
    "100%",
    strStyleCSS + "<body leftmargin=0 topmargin=0>" + str + "</body>"
  );
  // LODOP.ADD_PRINT_HTM(0, 0, "100%", "100%", str);
  LODOP.SET_PRINT_PAGESIZE(3, 5700, 50, "");
  // LODOP.ADD_PRINT_HTM(0, 0, "100%", "100%", document.getElementById("prints").innerHTML);
  // LODOP.SET_PRINT_PAGESIZE(3, 5700, 50, "");
}

function Preview2(str) {
  if (!LODOPbol) {
    return;
  }
  LODOP = getLodop();
  // //console.log(LODOP);
  if (LODOP) {
    LODOP.SET_PREVIEW_WINDOW(0, 1, 0, 800, 600, "");
    LODOP.SET_SHOW_MODE("PREVIEW_NO_MINIMIZE", true); //预览窗口禁止最小化，并始终最前
    LODOP.SET_PRINT_MODE("AUTO_CLOSE_PREWINDOW", 1);
    AddPrintContent2(str);
    LODOP.PRINT();
  } else {
    app && typeof app.noPrint === "function" && app.noPrint();
  }
}

function AddPrintContent2(str) {
  var strStyleCSS = "",
    styles = document.querySelectorAll("style,link");
  for (var i = 0; i < styles.length; i++) {
    strStyleCSS += styles[i].outerHTML;
  }
  // strStyleCSS += "<link href='../../UserControl/Print/CSS/bootstrapPrint.css' type='text/css' rel='stylesheet'>";
  LODOP.SET_PREVIEW_WINDOW(1, 0, 0, 0, 0, "");
  LODOP.SET_PRINT_MODE("PRINT_PAGE_PERCENT", "100%");
  LODOP.ADD_PRINT_HTM(
    5,
    5,
    "100%",
    "100%",
    strStyleCSS + "<body leftmargin=0 topmargin=0>" + str + "</body>"
  );
  // LODOP.ADD_PRINT_HTM(0, 0, "100%", "100%", str);
  LODOP.SET_PRINT_PAGESIZE(3, 5700, 50, "");
}
