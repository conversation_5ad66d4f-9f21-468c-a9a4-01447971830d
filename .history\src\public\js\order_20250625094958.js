var baseUrl = localStorage.getItem("fdb-domainName") + "/index.php?s=";

//pay组件
Vue.component("app-pay", function (resolve, reject) {
  $.get("component/pay.html").then(function (res) {
    resolve({
      template: res,
      props: {
        billToPay: {
          type: Number,
        },
        loginInfo: {
          type: Object,
        },
        "buy-receipt": {
          type: <PERSON>olean,
        },
        "order-no": {
          type: String,
        },
        "use-card": {
          type: Boolean,
        },
        isDebtFlag: {
          type: Boolean,
        },
      },
      data: function () {
        return {
          url: baseUrl,
          userInfo: {},
          cz_qudan: true, //收款弹窗
          composeMoney: "",
          isPay: 2,
          kd_kaidanxinxi: {}, // 收款开单信息
          kd_xinxi_list: {},
          kd_xinxi_list_buyer: {},
          kd_xinxi_list_cashierInfo: {}, //
          orderDetails: [],
          payCardInfo: [],
          requisiteCard: [],
          paymentOffer: [],
          checkedOffer: [],
          cz_shou_qian: "",
          paymentCode: "", //微信支付宝付款码

          cz_chongzhika: [],

          vipPass: "", //会员密码
          returnType: 3, // 1，多余定金转入会员默认账户，2，退回现金，3，多余定金原路退回
          orderNumber: "", // 订单号
          isCard: false,
          // 键盘
          value1: "1",
          value2: "2",
          value3: "3",
          zj_price_menu: [
            { key1: "7", key2: "8", key3: "9" },
            { key1: "4", key2: "5", key3: "6" },
            { key1: "1", key2: "2", key3: "3" },
            { key1: "00", key2: "0", key3: "•" },
          ],
          zj_all_num: [
            { key: "01", value: "7" },
            { key: "02", value: "8" },
            { key: "03", value: "9" },
            { key: "11", value: "4" },
            { key: "12", value: "5" },
            { key: "13", value: "6" },
            { key: "21", value: "1" },
            { key: "22", value: "2" },
            { key: "23", value: "3" },
            { key: "31", value: "00" },
            { key: "32", value: "0" },
            { key: "33", value: "." },
          ],
          selectId: "", //选中id
          // residuebalance: 0,
          remaining: 0, // 待支付

          paperwidth: 0, //打印纸宽度
          printSet: [], // 设置打印
          endPay: 0, //是否支付完成
          showMoney: 0,
          showMoneys: 0, //展示在待支付的money
          selectedCard: [], //选中的卡数组
          SelectPreferenceSequence: -1,
          cz_shou_qians: 0.0,

          //修改员工业绩
          isModifyPerformance: false,
          isModify: true,
          zuhekaPerformance: 1, //所有业绩提成信息
          totalPerformance: {},
          performanceList: [], //业绩提成信息列表
          salesmenList: [], //销售业绩列表
          techniciansList: [], //服务人员业绩列表
          isSales: false, //选择销售弹框
          isCrafts: false, //选择服务人员弹框
          isHandelIndex: 0, //每条服务在列表中的下标
          AllSales: [], //所有销售和服务人员
          AllCrafts: [], //所有服务人员
          deductType: [
            {
              name: "比例提成",
              id: 1,
            },
            {
              name: "固定提成",
              id: 2,
            },
          ], //提成方式下拉框
          isDeductType: "", //选择的提成方式
          salesChecked: [], //选中的销售存储
          craftsChecked: [], //存储选中的服务人员
          allDelect: [],
          saveModifyArr: [], //存储修改的数据
          addArr: [], //存储增添的数据
          delArr: [], //存储删除的数据
          //员工业绩
          isModify: false,
          loginModify: undefined,
          isReturnZero: false, //抹零
          formerMoney: 0, //抹零前的金额
          formrCash: 0, //抹零前的cash
          unChangedCash: 0, //不变的cash
          smallChangeMoney: 0, //抹零金额
          customizePayType: [], //自定义记账方式数组
          custmizePayType: 0, //自定义记账payType

          //会员消费弹框
          vipComsumeConfirm: false,
          vipPassComfirm: false,
          vipPassCodeComfirm: false,
          vipPassword: "",
          vipPassCode: "",

          //欠款
          isDebt: false, //欠款弹框
          debtForm: {
            orderMoney: 0,
            payMoney: "",
            debtMoney: 0,
          },
          isDebtMoney: false,
          debtFlag: false, //去支付欠账标志
        };
      },

      mounted: function () {
        this.fetchOrderDetail();
        // 获取小票样式
        this.getReceiptSet();
        this.loginModify = global.login;
        this.modifyEmployeeStatus = global.flag;
      },
      methods: {
        //监听键盘
        manualPrice: function () {
          this.cz_shou_qian = this.cz_shou_qian.replace(/[^\d\.]/g, "");
          this.cz_shou_qian = this.cz_shou_qian.replace(/^\./g, "");
          this.cz_shou_qian = this.cz_shou_qian.replace(/\.{2,}/g, ".");
          this.cz_shou_qian = this.cz_shou_qian
            .replace(".", "$#$")
            .replace(/\./g, "")
            .replace("$#$", ".");
          this.cz_shou_qian = this.cz_shou_qian.replace(
            /^(\-)*(\d+)\.(\d\d).*$/,
            "$1$2.$3"
          );
          this.cz_shou_qian = this.cz_shou_qian.replace(/^0.$/, "0.");
          if (this.cz_shou_qian > 100000.0) {
            this.cz_shou_qian = "100000.00";
          }
        },

        //欠款
        toDebt: function () {
          this.isDebt = true;
        },

        //欠款去支付关闭弹框事件
        cancelDebt: function () {
          this.isDebt = false;
        },

        //欠款input框事件
        handleDebtMoney: function (e) {
          let debtPayMoney = this.debtForm.payMoney;
          debtPayMoney = debtPayMoney.replace(/[^\d\.]/g, "");
          debtPayMoney = debtPayMoney.replace(/^\./g, "");
          debtPayMoney = debtPayMoney.replace(/\.{2,}/g, ".");
          debtPayMoney = debtPayMoney
            .replace(".", "$#$")
            .replace(/\./g, "")
            .replace("$#$", ".");
          debtPayMoney = debtPayMoney.replace(
            /^(\-)*(\d+)\.(\d\d).*$/,
            "$1$2.$3"
          );
          debtPayMoney = debtPayMoney.replace(/^0.$/, "0.");
          reg = /^\d{0,8}\.{0,1}(\d{1,2})?/g;
          let payMoneyArr = debtPayMoney.match(reg);
          let payMoney = payMoneyArr[0];
          //将输入框的值改要支付多少钱
          this.cz_shou_qian = payMoney;
          this.cash = Math.round(Number(payMoney) * 100);
          if (this.cash == this.debtForm.orderMoney) {
            this.isDebtMoney = false;
          } else {
            this.debtFlag = true;
            this.isDebtMoney = true;
          }
          if (payMoney) {
            let money = Math.round(Number(payMoney) * 100);
            this.debtForm.debtMoney =
              this.debtForm.orderMoney > money
                ? this.debtForm.orderMoney - money
                : 0;
            if (Number(e.target.value) === this.debtForm.orderMoney / 100) {
              this.debtForm.payMoney = e.target.value;
              return false;
            }
            this.debtForm.payMoney =
              this.debtForm.orderMoney > money
                ? debtPayMoney
                : (this.debtForm.orderMoney / 100).toFixed(2);
          }
        },

        //获取所有销售和服务人员
        getAllSales: function () {
          var _self = this;
          $.ajax({
            url: _self.url + "/android/Deduct/getStaff",
            type: "post",
            data: {
              merchantid: _self.loginInfo.merchantid,
              storeid: _self.loginInfo.storeid,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.AllSales = res.data;
              } else {
                _self.$message({
                  message: res.msg,
                  type: "warning",
                  duration: 1500,
                });
              }
            },
            error: function (error) {},
          });
        },

        //获取单据业绩提成记录
        modifyEmployeePerformance: function () {
          var _self = this;
          this.isModifyPerformance = true;

          // console.log(_self.loginInfo.merchantid,_self.loginInfo.storeid,_self.kd_xinxi_list.order_number)
          this.loading = true;
          $.ajax({
            url: _self.url + "/android/Deduct/getOrderDeductData",
            type: "post",
            data: {
              merchantid: _self.loginInfo.merchantid,
              orderNo: _self.kd_xinxi_list.order_number,
              storeid: _self.loginInfo.storeid,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.loading = false;
                _self.zuhekaPerformance = res.data.type;
                _self.totalPerformance = res.data;
                _self.performanceList = res.data.performance;
                // console.log(_self.performanceList[0].salesmen)
                let length = _self.performanceList.length - 1;
                for (let i = 0; i < _self.performanceList.length; i++) {
                  _self.performanceList[i].salesChecked = [];
                  _self.performanceList[i].craftsChecked = [];
                  if (_self.performanceList.base_amount) {
                  } else {
                    _self.performanceList[i].base_amount =
                      _self.performanceList[length].base_amount;
                  }
                }
              } else {
                _self.$message({
                  message: res.msg,
                  type: "warning",
                  duration: 1500,
                });
                _self.loading = false;
              }
            },
            error: function (error) {},
          });
          _self.getAllSales();
        },

        //选择销售
        chooseSales: function (performanceList, index) {
          var _self = this;
          this.isSales = true;
          this.isHandelIndex = index;
          _self.salesChecked =
            _self.performanceList[_self.isHandelIndex].salesChecked;
          for (let j = 0; j < _self.AllSales.length; j++) {
            Vue.delete(_self.AllSales[j], "isDisabled");
            for (
              let i = 0;
              i < _self.performanceList[_self.isHandelIndex].salesmen.length;
              i++
            ) {
              if (
                _self.performanceList[_self.isHandelIndex].salesmen[i]
                  .staff_id == _self.AllSales[j].id
              ) {
                _self.AllSales[j].isDisabled = 1;
              }
            }
          }
        },
        //添加销售
        addSalesmen: function () {
          var _self = this;
          this.isSales = false;
          let salesmenLength =
            _self.performanceList[_self.isHandelIndex].salesmen.length;
          // console.log(_self.salesChecked,"下标")
          _self.performanceList[_self.isHandelIndex].addSalesmen = []; //存储添加的销售
          _self.performanceList[_self.isHandelIndex].salesChecked =
            _self.salesChecked;
          for (let i = 0; i < _self.salesChecked.length; i++) {
            salesmenLength += 1;
            _self.performanceList[_self.isHandelIndex].addSalesmen[i] = {
              staffName: _self.AllSales[_self.salesChecked[i]].nickname,
              lengthh: salesmenLength,
              assign: 2,
              base_amount:
                _self.performanceList[_self.isHandelIndex].base_amount,
              commission: 0.0,
              commission_proportion: 0.0,
              performance: 0.0,
              performance_proportion: 0.0,
              deduct_type: 1,
              deduct_way: 1,
              order_time: _self.totalPerformance.order_time,
              staff_id: _self.AllSales[_self.salesChecked[i]].id,
              storeid: _self.totalPerformance.storeid,
              merchantid: _self.totalPerformance.merchantid,
              id: 0,
              order_id: _self.performanceList[_self.isHandelIndex].order_id,
              order_detail_id: _self.performanceList[_self.isHandelIndex].id,
            };
          }
        },

        //选择服务人员
        chooseCrafts: function (performanceList, index) {
          var _self = this;
          this.isCrafts = true;
          this.isHandelIndex = index;
          _self.AllCrafts = _self.AllSales.filter(function (items, index, ar) {
            if (items.isTech == 2) {
              return items;
            }
          });
          _self.craftsChecked =
            _self.performanceList[_self.isHandelIndex].craftsChecked;
          for (let j = 0; j < _self.AllCrafts.length; j++) {
            Vue.delete(_self.AllCrafts[j], "isDisabled");
            for (
              let i = 0;
              i < _self.performanceList[_self.isHandelIndex].technicians.length;
              i++
            ) {
              if (
                _self.performanceList[_self.isHandelIndex].technicians[i]
                  .staff_id == _self.AllCrafts[j].id
              ) {
                _self.AllCrafts[j].isDisabled = 1;
              }
            }
          }
        },
        //添加服务人员
        addCrafts: function () {
          var _self = this;
          this.isCrafts = false;
          let craftsLength =
            _self.performanceList[_self.isHandelIndex].technicians.length;
          _self.performanceList[_self.isHandelIndex].addCrafts = [];
          _self.performanceList[_self.isHandelIndex].craftsChecked =
            _self.craftsChecked;
          for (let i = 0; i < _self.craftsChecked.length; i++) {
            craftsLength += 1;
            _self.performanceList[_self.isHandelIndex].addCrafts[i] = {
              staffName: _self.AllCrafts[_self.craftsChecked[i]].nickname,
              lengthh: craftsLength,
              assign: 2,
              base_amount:
                _self.performanceList[_self.isHandelIndex].base_amount,
              commission: 0.0,
              commission_proportion: 0.0,
              performance: 0.0,
              performance_proportion: 0.0,
              deduct_type: 2,
              deduct_way: 1,
              order_time: _self.totalPerformance.order_time,
              staff_id: _self.AllCrafts[_self.craftsChecked[i]].id,
              storeid: _self.totalPerformance.storeid,
              merchantid: _self.totalPerformance.merchantid,
              id: 0,
              order_id: _self.performanceList[_self.isHandelIndex].order_id,
              order_detail_id: _self.performanceList[_self.isHandelIndex].id,
            };
          }
        },

        //选择提成方式
        chooseDeductType: function (e, sindex, lindex) {
          this.$forceUpdate();
        },

        limitInput: function (e) {
          // e.target.value = e.target.value.replace(/[^\d\.]/g, '')
          // e.target.value = e.target.value.replace(/^\./g, '0.');
          // e.target.value = e.target.value.replace(/\.{2,}/g, '.');
          // e.target.value = e.target.value.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.');
          // e.target.value = e.target.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
          // e.target.value = e.target.value.replace(/^0.$/, '0.');
        },
        //输入金额呈现百分比
        limitInputMoney: function (e) {
          //e.target._value 当前操作值
          //$(e.path[2]).find('input')[1].value  改变值
          //parseInt(e.path[7].children[0].childNodes[0].textContent)-1  每一项服务在服务数组中下标
          //parseInt(e.path[3].children[0].childNodes[0].textContent)-1 销售或服务人员数组中每一条数据的下标
          let performanceIndex =
            parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
          let lineIndex =
            parseInt(e.path[3].children[0].childNodes[0].textContent) - 1;
          let per = (
            (e.target.value * 10000) /
            this.performanceList[performanceIndex].base_amount
          ).toFixed(2);
          $(e.path[2]).find("input")[1].value = per;
          if (e.path[5].children[0].textContent == "选择销售") {
            this.performanceList[performanceIndex].salesmen[
              lineIndex
            ].performance_proportion = per.toString();
          } else {
            this.performanceList[performanceIndex].technicians[
              lineIndex
            ].performance_proportion = per.toString();
          }
        },
        limitInputMoneyAdd: function (e, items, index) {
          let performanceIndex =
            parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
          $(e.path[2]).find("input")[1].value = (
            (parseInt(items.performance) * 10000) /
            items.base_amount
          ).toFixed(2);
          if (e.path[5].children[0].textContent == "选择销售") {
            this.performanceList[performanceIndex].addSalesmen[
              index
            ].performance_proportion = (
              (parseInt(items.performance) * 10000) /
              items.base_amount
            ).toFixed(2);
          } else {
            this.performanceList[performanceIndex].addCrafts[
              index
            ].performance_proportion = (
              (parseInt(items.performance) * 10000) /
              items.base_amount
            ).toFixed(2);
          }
          this.$forceUpdate();
        },
        limitInputMoneyAdd1: function (e, items, index) {
          let performanceIndex =
            parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
          $(e.path[2]).find("input")[1].value = (
            (parseInt(items.commission) * 10000) /
            items.base_amount
          ).toFixed(2);
          if (e.path[5].children[0].textContent == "选择销售") {
            this.performanceList[performanceIndex].addSalesmen[
              index
            ].commission_proportion = (
              (parseInt(items.commission) * 10000) /
              items.base_amount
            ).toFixed(2);
          } else {
            this.performanceList[performanceIndex].addCrafts[
              index
            ].commission_proportion = (
              (parseInt(items.commission) * 10000) /
              items.base_amount
            ).toFixed(2);
          }
          this.$forceUpdate();
        },
        limitInputMoney1: function (e) {
          let performanceIndex =
            parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
          let lineIndex =
            parseInt(e.path[3].children[0].childNodes[0].textContent) - 1;
          let per = (
            (e.target.value * 10000) /
            this.performanceList[performanceIndex].base_amount
          ).toFixed(2);
          $(e.path[2]).find("input")[1].value = per;
          if (e.path[5].children[0].textContent == "选择销售") {
            this.performanceList[performanceIndex].salesmen[
              lineIndex
            ].commission_proportion = per.toString();
          } else {
            this.performanceList[performanceIndex].technicians[
              lineIndex
            ].commission_proportion = per.toString();
          }
        },
        //输入百分比呈现金额
        limitInputPer: function (e) {
          //e.target._value 当前操作值
          //e.path[2]).find('input')[0].value 改变值
          let performanceMoney =
            parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
          let lineIndex =
            parseInt(e.path[3].children[0].childNodes[0].textContent) - 1;
          let money = (
            (e.target.value *
              this.performanceList[performanceMoney].base_amount) /
            10000
          ).toFixed(2);
          $(e.path[2]).find("input")[0].value = money;
          if (e.path[5].children[0].textContent == "选择销售") {
            this.performanceList[performanceMoney].salesmen[
              lineIndex
            ].performance = money.toString();
          } else {
            this.performanceList[performanceMoney].technicians[
              lineIndex
            ].performance = money.toString();
          }
        },
        limitInputPer1: function (e) {
          let performanceMoney =
            parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
          let lineIndex =
            parseInt(e.path[3].children[0].childNodes[0].textContent) - 1;
          let money = (
            (e.target.value *
              this.performanceList[performanceMoney].base_amount) /
            10000
          ).toFixed(2);
          $(e.path[2]).find("input")[0].value = money;
          if (e.path[5].children[0].textContent == "选择销售") {
            this.performanceList[performanceMoney].salesmen[
              lineIndex
            ].commission = money.toString();
          } else {
            this.performanceList[performanceMoney].technicians[
              lineIndex
            ].commission = money.toString();
          }
        },
        limitInputPerAdd: function (e, items, index) {
          let performanceMoney =
            parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
          $(e.path[2]).find("input")[0].value = (
            (parseInt(items.performance_proportion) * items.base_amount) /
            10000
          ).toFixed(2);
          if (e.path[5].children[0].textContent == "选择销售") {
            this.performanceList[performanceMoney].addSalesmen[
              index
            ].performance = (
              (parseInt(items.performance_proportion) * items.base_amount) /
              10000
            ).toFixed(2);
          } else {
            this.performanceList[performanceMoney].addCrafts[
              index
            ].performance = (
              (parseInt(items.performance_proportion) * items.base_amount) /
              10000
            ).toFixed(2);
          }
          this.$forceUpdate();
        },
        limitInputPerAdd1: function (e, items, index) {
          let performanceMoney =
            parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
          $(e.path[2]).find("input")[0].value = (
            (parseInt(items.commission_proportion) * items.base_amount) /
            10000
          ).toFixed(2);
          if (e.path[5].children[0].textContent == "选择销售") {
            this.performanceList[performanceMoney].addSalesmen[
              index
            ].commission = (
              (parseInt(items.commission_proportion) * items.base_amount) /
              10000
            ).toFixed(2);
          } else {
            this.performanceList[performanceMoney].addCrafts[index].commission =
              (
                (parseInt(items.commission_proportion) * items.base_amount) /
                10000
              ).toFixed(2);
          }
          this.$forceUpdate();
        },

        //删除销售、服务人员
        delectsalesmen: function (info, index, inde) {
          var _self = this;
          _self.allDelect.push(_self.performanceList[inde].salesmen[index]);
          _self.performanceList[inde].salesmen.splice(index, 1);
          this.$forceUpdate();
        },
        delectCrafts: function (info, index, inde) {
          var _self = this;
          _self.allDelect.push(_self.performanceList[inde].technicians[index]);
          _self.performanceList[inde].technicians.splice(index, 1);
          this.$forceUpdate();
        },

        //提交保存修改的数据
        saveModify: function () {
          var _self = this;
          _self.loading = true;
          _self.saveModifyArr = [];
          _self.delArr = [];
          _self.addArr = [];
          //遍历拼接数组
          for (let i = 0; i < _self.performanceList.length; i++) {
            _self.saveModifyArr = _self.saveModifyArr.concat(
              _self.performanceList[i].salesmen,
              _self.performanceList[i].technicians
            );
            if (_self.performanceList[i].addSalesmen) {
              if (_self.performanceList[i].addCrafts) {
                _self.addArr = _self.addArr.concat(
                  _self.performanceList[i].addSalesmen,
                  _self.performanceList[i].addCrafts
                );
              } else {
                _self.addArr = _self.addArr.concat(
                  _self.performanceList[i].addSalesmen
                );
              }
            } else {
              if (_self.performanceList[i].addCrafts) {
                _self.addArr = _self.addArr.concat(
                  _self.performanceList[i].addCrafts
                );
              } else {
                _self.addArr = _self.addArr;
              }
            }
          }
          //删除多余的属性
          for (let i = 0; i < _self.addArr.length; i++) {
            Vue.delete(_self.addArr[i], "lengthh");
          }
          _self.delArr = _self.allDelect;
          _self.saveModifyArr = JSON.stringify(_self.saveModifyArr);
          _self.delArr = JSON.stringify(_self.delArr);
          _self.addArr = JSON.stringify(_self.addArr);
          $.ajax({
            url: _self.url + "/android/Deduct/saveDeductData",
            type: "post",
            data: {
              merchantid: _self.loginInfo.merchantid,
              orderNo: _self.kd_xinxi_list.order_number,
              storeid: _self.loginInfo.storeid,
              addArr: _self.addArr,
              delArr: _self.delArr,
              saveArr: _self.saveModifyArr,
              nickname: _self.loginInfo.nickname,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.loading = false;
                _self.$message({
                  message: res.msg,
                  type: "success",
                  duration: 1500,
                });
                _self.isModifyPerformance = false;
              } else {
                _self.$message({
                  message: res.msg,
                  type: "warning",
                  duration: 1500,
                });
                _self.loading = false;
              }
            },
            error: function (error) {},
          });

          // console.log(_self.addArr)
        },

        //新建会员开单
        newMemberBilling: function () {
          //window.location.href = "cashier_system.html?phone=" + this.kd_xinxi_list_buyer.phone + "&flag=3";
          let href =
            "cashier_system.html?phone=" +
            this.kd_xinxi_list_buyer.phone +
            "&flag=3";

          top.app.toPage(href);
        },

        //获取自定义记账方式
        getCustomizePayType: function () {
          var _self = this;
          $.ajax({
            url: _self.url + "/android/Pay/getCustomizePayType",
            type: "POST",
            data: {
              merchantid: _self.loginInfo.merchantid,
              storeid: _self.loginInfo.storeid,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.customizePayType = res.data;
              }
            },
            error: function (err) {},
          });
        },

        //选择自定义记账方式
        chooseCustmizePayType: function (payType) {
          this.custmizePayType = payType;
        },

        //自定义支付
        custmizePayTypeBill: function () {
          let _self = this;
          var selectOffer = _self.selectedCard.concat(_self.requisiteCard);
          var arr = [];

          if (this.custmizePayType == 0) {
            _self.$message({
              type: "warning",
              message: "请选择记账方式",
              duration: 1500,
            });
            return false;
          }

          for (var i = 0; i < selectOffer.length; i++) {
            arr.push({
              cardId: selectOffer[i].id,
              money: selectOffer[i].realPay || selectOffer[i].moneydiscount,
            });
          }
          if (
            _self.kd_xinxi_list.needVipPass &&
            _self.kd_xinxi_list.smallMoney < _self.kd_xinxi_list.vipWorth
          ) {
            _self.vipComsumeConfirm = true;
            return false;
          }

          const loading = this.$loading({
            lock: true,
            text: "支付...",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          let url = "/android/pay/pay";
          if (_self.$props.isDebtFlag) {
            url = "/android/pay/payForDebt";
          }
          $.ajax({
            url: _self.url + url,
            type: "post",
            data: {
              smallChangeMoney: _self.smallChangeMoney,
              cardArr: JSON.stringify(arr),
              code: _self.paymentCode,
              debug: 0,
              orderNo: _self.kd_xinxi_list.order_number,
              payInfo: "",
              payType: _self.custmizePayType,
              returnType: _self.returnType,
              vipCode: "",

              vipPass: 0,
              merchantid: _self.userInfo.merchantid,
              storeid: _self.userInfo.storeid,
              cashier_id: _self.userInfo.id,
              shift_no: _self.userInfo.shift_no,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                loading.close();
                _self.$message({
                  type: "success",
                  message: res.msg,
                  duration: 1500,
                  onClose: function () {
                    _self.endPay = 1;
                    _self.kd_xinxi_list.state = 4;
                  },
                });
                _self.getOrderDetails();
                _self.$forceUpdate();
              } else {
                // _self.loading = false;
                loading.close();
                _self.$message({
                  type: "error",
                  message: res.msg,
                  duration: 1500,
                });
              }
            },
          });
        },

        //计算展示价格的值
        deleteDiscountBalance: function () {
          var _self = this;

          for (let i = 0; i < _self.orderDetails.length; i++) {
            if (_self.orderDetails[i]["equity_type"] == 1) {
              _self.showMoney += _self.orderDetails[i]["price"];
            } else if (_self.orderDetails[i]["equity_type"] == 2) {
              if (_self.orderDetails[i]["card_id"] == 0) {
                _self.showMoney += _self.orderDetails[i]["smallTotal"];
              } else {
                for (let j = 0; j < _self.payCardInfo.length; j++) {
                  if (
                    _self.orderDetails[i]["card_id"] ==
                    _self.payCardInfo[j]["id"]
                  ) {
                    _self.payCardInfo[j]["residuebalance"] -=
                      _self.orderDetails[i]["smallTotal"];
                  }
                }
              }
            } else if (_self.orderDetails[i]["equity_type"] == 3) {
              _self.showMoney += 0;
            } else {
              _self.showMoney += _self.orderDetails[i]["smallTotal"];
            }
          }

          _self.showMoneys = (_self.showMoney / 100).toFixed(2);
        },

        inputFocus: function (dom) {
          this.$nextTick(
            function () {
              dom.focus();
              // $(this.$refs.actualHarvest).focus()
            }.bind(this)
          );
        },
        bindPaymentOffer: function (item, index) {
          var _self = this;
          //定义一个变量来记录点击的折扣与待支付相见的结果
          var dealPrice = 0;
          var dealPrice1 = 0;

          var selectCardLength = _self.selectedCard.length;
          //当选中一张卡，会加入moneydiscount表示这张卡抵扣了多少钱。
          if (selectCardLength == 0 && _self.showMoney == 0) {
            //来到收款页面，选择余额。所选的都已经是折扣或者抵扣、待支付直接为0.所欲不可以点击可选卡
            _self.SelectPreferenceSequence = -1;
          } else {
            if (_self.showMoney == 0 && selectCardLength != 0) {
              //当前已经选过了权益卡并且现在的待支付为0
              //此时点击其他的卡不会有什么变化，只能点击已经选择卡，取消已经选的权益。不管已经选了几个权益卡。最多也只能选俩个。
              if (selectCardLength == 1) {
                if (_self.selectedCard[0]["id"] == item["id"]) {
                  //现在待支付是0，且选中了一个但是要取消。
                  for (let i = 0; i < _self.payCardInfo.length; i++) {
                    if (item["id"] == _self.payCardInfo[i]["id"]) {
                      _self.payCardInfo[i]["residuebalance"] +=
                        _self.selectedCard[0]["moneydiscount"];
                      _self.showMoney = _self.selectedCard[0]["moneydiscount"];
                      _self.showMoneys = (_self.showMoney / 100).toFixed(2);
                      _self.selectedCard.splice(0, 1);
                      _self.SelectPreferenceSequence = -1;

                      break;
                    }
                  }
                } else {
                }
              } else {
                if (_self.selectedCard[0]["id"] == item["id"]) {
                  for (let i = 0; i < _self.payCardInfo.length; i++) {
                    if (item["id"] == _self.payCardInfo[i]["id"]) {
                      _self.payCardInfo[i]["residuebalance"] +=
                        _self.selectedCard[0]["moneydiscount"];
                      _self.showMoney = _self.selectedCard[0]["moneydiscount"];
                      _self.showMoneys = (_self.showMoney / 100).toFixed(2);
                      _self.selectedCard.splice(0, 1);
                      _self.SelectPreferenceSequence = -1;

                      break;
                    }
                  }
                  for (let i = 0; i < _self.payCardInfo.length; i++) {
                    if (
                      _self.selectedCard[0]["id"] == _self.payCardInfo[i]["id"]
                    ) {
                      if (
                        _self.payCardInfo[i]["residuebalance"] >=
                        _self.showMoney
                      ) {
                        _self.payCardInfo[i]["residuebalance"] -=
                          _self.showMoney;
                        _self.selectedCard[0]["moneydiscount"] +=
                          _self.showMoney;
                        _self.showMoney = 0;
                        _self.showMoneys = _self.showMoney;
                      } else {
                        dealPrice =
                          _self.payCardInfo[i]["residuebalance"] -
                          _self.showMoney;
                        _self.payCardInfo[i]["residuebalance"] = 0;
                        _self.selectedCard[0]["moneydiscount"] +=
                          _self.payCardInfo[i]["residuebalance"];
                        _self.showMoney = Math.abs(dealPrice);
                        _self.showMoneys = (_self.showMoney / 100).toFixed(2);
                      }
                    }
                  }
                } else if (_self.selectedCard[1]["id"] == item["id"]) {
                  for (let i = 0; i < _self.payCardInfo.length; i++) {
                    if (item["id"] == _self.payCardInfo[i]["id"]) {
                      _self.payCardInfo[i]["residuebalance"] +=
                        _self.selectedCard[0]["moneydiscount"];
                      _self.showMoney = _self.selectedCard[0]["moneydiscount"];
                      _self.showMoneys = (_self.showMoney / 100).toFixed(2);
                      _self.selectedCard.splice(1, 1);
                      _self.SelectPreferenceSequence = -1;

                      break;
                    }
                  }
                  for (let i = 0; i < _self.payCardInfo.length; i++) {
                    if (
                      _self.selectedCard[0]["id"] == _self.payCardInfo[i]["id"]
                    ) {
                      if (
                        _self.payCardInfo[i]["residuebalance"] >=
                        _self.showMoney
                      ) {
                        _self.payCardInfo[i]["residuebalance"] -=
                          _self.showMoney;
                        _self.selectedCard[0]["moneydiscount"] +=
                          _self.showMoney;
                        _self.showMoney = 0;
                        _self.showMoneys = _self.showMoney;
                      } else {
                        dealPrice =
                          _self.payCardInfo[i]["residuebalance"] -
                          _self.showMoney;
                        _self.payCardInfo[i]["residuebalance"] = 0;
                        _self.selectedCard[0]["moneydiscount"] +=
                          _self.payCardInfo[i]["residuebalance"];
                        _self.showMoney = Math.abs(dealPrice);
                        _self.showMoneys = (_self.showMoney / 100).toFixed(2);
                      }
                    }
                  }
                }
              }
            } else if (_self.showMoney != 0 && selectCardLength == 0) {
              for (let i = 0; i < _self.payCardInfo.length; i++) {
                if (_self.payCardInfo[i]["id"] == item["id"]) {
                  if (
                    _self.payCardInfo[i]["residuebalance"] >= _self.showMoney
                  ) {
                    _self.payCardInfo[i]["residuebalance"] -= _self.showMoney;
                    item["moneydiscount"] = _self.showMoney;
                    _self.showMoney = 0;
                    _self.showMoneys = _self.showMoney;
                  } else {
                    dealPrice = _self.payCardInfo[i]["residuebalance"] -=
                      _self.showMoney;
                    _self.payCardInfo[i]["residuebalance"] = 0;
                    item["moneydiscount"] +=
                      _self.payCardInfo[i]["residuebalance"];
                    _self.showMoney = Math.abs(dealPrice);
                    _self.showMoneys = (_self.showMoney / 100).toFixed(2);
                  }
                  break;
                }
              }
              _self.selectedCard.push(item);
              _self.SelectPreferenceSequence = index;
            } else if (_self.showMoney != 0 && selectCardLength != 0) {
              if (selectCardLength == 1) {
                if (item["id"] == _self.selectedCard[0]["id"]) {
                  for (let i = 0; i < _self.payCardInfo.length; i++) {
                    if (item["id"] == _self.payCardInfo[i]["id"]) {
                      _self.payCardInfo[i]["residuebalance"] +=
                        _self.selectedCard[0]["moneydiscount"];
                      _self.showMoney += _self.selectedCard[0]["moneydiscount"];
                      _self.showMoneys = (_self.showMoney / 100).toFixed(2);
                    }
                  }
                } else {
                  for (let i = 0; i < _self.payCardInfo.length; i++) {
                    if (item["id"] == _self.payCardInfo[i]["id"]) {
                      if (
                        _self.payCardInfo[i]["residuebalance"] >=
                        _self.showMoney
                      ) {
                        _self.payCardInfo[i]["residuebalance"] -=
                          _self.showMoney;
                        item["moneydiscount"] = _self.showMoney;
                        _self.showMoney = 0;
                        _self.showMoneys = _self.showMoney;
                      } else {
                        dealPrice = _self.payCardInfo[i]["residuebalance"] -=
                          _self.showMoney;
                        _self.payCardInfo[i]["residuebalance"] = 0;
                        item["moneydiscount"] +=
                          _self.payCardInfo[i]["residuebalance"];
                        _self.showMoney = Math.abs(dealPrice);
                        _self.showMoneys = (_self.showMoney / 100).toFixed(2);
                      }
                      break;
                    }
                  }
                  _self.selectedCard.push(item);
                  _self.SelectPreferenceSequence = index;
                }
              } else {
                //待支付不为0 且长度为2

                if (_self.selectedCard[0]["id"] == item["id"]) {
                  for (let i = 0; i < _self.payCardInfo.length; i++) {
                    if (item["id"] == _self.payCardInfo[i]["id"]) {
                      _self.payCardInfo[i]["residuebalance"] +=
                        _self.selectedCard[0]["moneydiscount"];
                      _self.showMoney = _self.selectedCard[0]["moneydiscount"];
                      _self.showMoneys = (_self.showMoney / 100).toFixed(2);
                      _self.selectedCard.splice(0, 1);
                      _self.SelectPreferenceSequence = -1;

                      break;
                    }
                  }
                } else if (_self.selectedCard[1]["id"] == item["id"]) {
                  for (let i = 0; i < _self.payCardInfo.length; i++) {
                    if (item["id"] == _self.payCardInfo[i]["id"]) {
                      _self.payCardInfo[i]["residuebalance"] +=
                        _self.selectedCard[0]["moneydiscount"];
                      _self.showMoney = _self.selectedCard[0]["moneydiscount"];
                      _self.showMoneys = (_self.showMoney / 100).toFixed(2);
                      _self.selectedCard.splice(1, 1);
                      _self.SelectPreferenceSequence = -1;

                      break;
                    }
                  }
                }
              }
            }
          }
          _self.cz_shou_qians = _self.showMoneys;
        },

        paymentChecked: function (id) {
          for (var i = 0; i < this.checkedOffer.length; i++) {
            if (this.checkedOffer[i].id == id) {
              return true;
            }
          }
          return false;
        },

        bindPay: function (pay) {
          this.isPay = pay;
          switch (pay) {
            case 1:
              this.$nextTick(function () {
                $(this.$refs.paymentCode).focus();
              });
              break;
            case 2:
              this.$nextTick(function () {
                $(this.$refs.actualHarvest).focus();
              });
              break;
            case 4:
              this.getCustomizePayType();
              break;
          }
        },

        // 获取订单内容
        fetchOrderDetail: function () {
          // _self.kd_xinxi_list.orderNo
          var _self = this;
          let data = {
            merchantid: _self.userInfo.merchantid,
            orderNo: _self.orderNumber,
            storeid: _self.userInfo.storeid,
          };
          if (_self.$props.isDebtFlag) {
            data.repayment = 1;
          } else {
            data.repayment = 0;
          }
          this.loading = true;
          $.ajax({
            url: _self.url + "/android/order/getOrderDetail",
            type: "post",
            data: data,
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.loading = false;
                _self.isPay = 2;
                _self.kd_xinxi_list = res.data;
                _self.kd_xinxi_list_buyer = res.data.buyer;
                _self.kd_xinxi_list_cashierInfo = res.data.cashierInfo;
                _self.composeMoney = res.data.toBePay / 100;
                //订单号详情
                // _self.orderDetails.orderInfo = res.data;
                //会员已有卡的全部信息
                _self.payCardInfo = res.data.payCardInfo;

                _self.requisiteCard = res.data.requisiteCard;
                // if (_self.cz_qudan) {
                //     _self.$nextTick(function () {
                //         _self.$refs.actualHarvest.focus()
                //     }.bind(this))
                // }
                //当请求道数据后，开始进行先直接扣去折扣卡的优惠的价格，在展示还需要支付的钱。

                _self.getOrderDetails(res.data.id);
                _self.deleteDiscountBalance();
              } else {
                _self.$message({
                  message: res.msg,
                  type: "warning",
                  duration: 1500,
                });
                _self.loading = false;
              }
            },
            error: function (error) {},
          });
        },

        //获取订单详情
        getOrderDetails: function (id) {
          var _self = this;
          $.ajax({
            url: _self.url + "/android/Orderlist/OrderDetails",
            type: "POST",
            data: {
              id: id,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.kd_xinxi_list.collection_time = res.data.collection_time;
                _self.orderDetails = res.data;
                if (_self.$props.isDebtFlag) {
                  _self.orderDetails.alreadyPay =
                    _self.kd_xinxi_list.toBePay - res.data.debt_value;
                  _self.kd_xinxi_list.toBePay = res.data.debt_value;
                  _self.cash = _self.kd_xinxi_list.toBePay;

                  _self.cz_shou_qian = String(_self.cash / 100);
                  _self.unChangedCash = String(_self.cash / 100);
                }
              }
            },
            error: function (err) {},
          });
        },

        //微信支付宝付款
        phonePay() {
          if (
            this.kd_xinxi_list.needVipPass &&
            this.kd_xinxi_list.smallMoney < this.kd_xinxi_list.vipWorth
          ) {
            this.vipComsumeConfirm = true;
          } else {
            this.pay(0);
          }
        },

        pay: function (vipPass) {
          var _self = this;
          var payType = 0; //支付类型 1微信 2支付宝 3现金 4小程序微信 5会员余额
          if (this.isPay == 0) {
            payType = 5;
          } else if (this.isPay == 1) {
            payType = 1;
          } else if (this.isPay == 2) {
            payType = 3;
          } else if (this.isPay == 4) {
            payType = _self.custmizePayType;
          }
          var selectOffer = _self.selectedCard.concat(_self.requisiteCard);
          var arr = [];

          for (var i = 0; i < selectOffer.length; i++) {
            arr.push({
              cardId: selectOffer[i].id,
              money: selectOffer[i].realPay || selectOffer[i].moneydiscount,
            });
          }

          const loading = this.$loading({
            lock: true,
            text: "支付...",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          let url = "/android/pay/pay";
          if (_self.$props.isDebtFlag) {
            url = "/android/pay/payForDebt";
            arr = [];
          }
          $.ajax({
            url: _self.url + url,
            type: "post",
            data: {
              smallChangeMoney: _self.smallChangeMoney,
              cardArr: JSON.stringify(arr),
              code: _self.paymentCode,
              debug: 0,
              orderNo: _self.kd_xinxi_list.order_number,
              payInfo: "",
              payType: payType,
              returnType: _self.returnType,
              vipCode: "",
              vipPass: vipPass,
              merchantid: _self.userInfo.merchantid,
              storeid: _self.userInfo.storeid,
              cashier_id: _self.userInfo.id,
              shift_no: _self.userInfo.shift_no,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                // _self.loading = false;
                _self.$message({
                  type: "success",
                  message: res.msg,
                  duration: 1500,
                  onClose: function () {
                    _self.endPay = 1;
                    _self.kd_xinxi_list.state = 4;
                  },
                });
                loading.close();
              } else {
                // _self.loading = false;
                loading.close();
                _self.$message({
                  type: "error",
                  message: res.msg,
                  duration: 1500,
                });
              }
            },
          });
        },

        //会员动态码
        vipPasswordCodeComfirm: function () {
          var reg = /^\d{14}$/g;
          // debugger
          if (reg.test(this.vipPassCode)) {
            this.pay(this.vipPassCode);
          } else {
            // console.log("密码输错了")
            this.$message({
              type: "warning",
              message: "动态码输入有误",
              duration: 1500,
            });
          }
        },

        //会员动态码
        vipPassCodeComfirmAlert: function () {
          let _self = this;
          this.vipPassCodeComfirm = true;
          setTimeout(function () {
            _self.inputFocus(_self.$refs.inputVipPasswordCode);
          }, 100);
        },

        //会员密码
        vipPasswordComfirm: function () {
          var reg = /^\d{6}$/g;
          // debugger
          if (reg.test(this.vipPassword)) {
            this.pay(this.vipPassword);
          } else {
            this.$message({
              type: "warning",
              message: "密码输入有误",
              duration: 1500,
            });
          }
        },

        //会员密码
        vipPasswordAlert: function () {
          let _self = this;
          this.vipPassComfirm = true;
          setTimeout(function () {
            _self.inputFocus(_self.$refs.inputVipPassword);
          }, 100);
        },

        // 确认收款(收银支付)
        payTheBill: function () {
          var _self = this;

          if (_self.isPay == 0 || _self.isPay == 2) {
            if (
              _self.kd_xinxi_list.needVipPass &&
              _self.kd_xinxi_list.smallMoney < _self.kd_xinxi_list.vipWorth
            ) {
              _self.vipComsumeConfirm = true;
            } else {
              let useMoney = Number(this.cz_shou_qian);
              let total = useMoney - Number(this.cash) / 100;
              if (!this.cz_shou_qian) {
                this.$message({
                  type: "error",
                  message: "输入支付金额",
                  duration: 1500,
                });
                return false;
              }
              if (total < 0) {
                this.$message({
                  type: "error",
                  message: "支付金额不足",
                  duration: 1500,
                });
                return false;
              }
              _self.pay();
            }
          } else if (_self.isPay == 1) {
            if (!this.paymentCode) {
              this.$message({
                type: "error",
                message: "付款码不能为空",
                duration: 1500,
              });
              return false;
            }
            _self.pay();
          }
        },

        // 支付完成
        newBilling: function () {
          this.$emit("close-pay", false);
          this.clearData();
          location.href = "cashier_system.html";
        },

        // (清空页面数据)收款数据
        clearData: function () {
          this.kd_kaidanxinxi = []; // 收款开单信息
          this.kd_xinxi_list = {};
          this.kd_xinxi_list_buyer = {};
          this.kd_xinxi_list_cashierInfo = {}; //
          this.orderDetails = [];
          this.payCardInfo = [];
          this.paymentOffer = [];
          this.checkedOffer = [];
          this.cz_shou_qian = "";
          this.paymentCode = ""; //微信支付宝付款码
          this.vipPass = ""; //会员密码
          this.returnType = 3;
        },

        cz_qudan_close: function () {
          // this.cz_qudan = false;
          this.$emit("close-pay", false);
        },

        /**键盘**/
        cz_del_all: function () {
          this.cz_shou_qian = "";
          this.inputFocus(this.$refs.actualHarvest);
        },

        cz_del_one: function () {
          this.inputFocus(this.$refs.actualHarvest);
          let input_lenth = this.cz_shou_qian.length;
          if (this.cz_shou_qian.length > 0) {
            this.cz_shou_qian = this.cz_shou_qian.slice(0, input_lenth - 1);
          }
        },

        cz_input_num: function (index, value1) {
          this.inputFocus(this.$refs.actualHarvest);
          let isdian = this.cz_shou_qian.indexOf(".");
          //console.log('dian' + isdian);
          let input_lenth = this.cz_shou_qian.length;
          let get_hlnum = index + value1;
          let cha_num = input_lenth - isdian - 1;

          if (input_lenth == 0) {
            if (get_hlnum == "33" || get_hlnum == "31" || get_hlnum == "32") {
              this.cz_shou_qian += "0.";
            } else {
              for (let i of this.zj_all_num) {
                // //console.log(i.key);
                if (i.key == get_hlnum) {
                  this.cz_shou_qian += i.value;
                }
              }
            }
          } else {
            if (isdian == -1) {
              for (let i of this.zj_all_num) {
                if (i.key == get_hlnum) {
                  this.cz_shou_qian += i.value;
                }
              }
              if (this.cz_shou_qian > 100000.0) {
                this.cz_shou_qian = "100000.00";
                this.$message({
                  showClose: true,
                  message: "最大付款金额10万",
                  type: "warning",
                });
                let that = this;
                setTimeout(function () {
                  that.zj_max_price = true;
                }, 200);
                setTimeout(function () {
                  that.zj_max_price = false;
                }, 1200);
              }
            } else if (this.cz_shou_qian == "0.") {
              if (cha_num * 1 < 2) {
                for (let i of this.zj_all_num) {
                  if (!(get_hlnum == "33")) {
                    if (i.key == get_hlnum) {
                      this.cz_shou_qian += i.value;
                    }
                  }
                }
              }
            } else {
              if (cha_num < 2) {
                for (let i of this.zj_all_num) {
                  // //console.log(i.key);
                  if (i.key == get_hlnum) {
                    this.cz_shou_qian += i.value;
                  }
                }
              }
            }
          }
        },

        // 获取小票样式
        getReceiptSet: function () {
          var _self = this;

          $.ajax({
            url: _self.url + "/android/order/getReceiptSet",
            type: "post",
            data: {
              merchantid: _self.loginInfo.merchantid,
              storeid: _self.loginInfo.storeid,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.printSet = res.data;
                _self.paperwidth = res.data.width;
              } else {
                let data =
                  '{"id":1,"storeid":1,"merchantid":1,"name":"默认小票","status":1,"addtime":"2020-01-01 08:00","set":[{"name":"store","set":{"fontSize":20}},{"name":"header","set":{"fontSize":12,"text":"收银小票","headerfontSize":18,"ordertype":1,"ordernum":1,"ordertime":1,"num":1,"cashier":1}},{"name":"goods","set":{"fontSize":12,"goodsname":1,"price":1,"num":1,"allmoney":1,"sku":1}},{"name":"vip","set":{"fontSize":12,"name":1,"cardnum":1,"money":1,"score":1}},{"name":"takegoods","set":{"fontSize":12,"name":1,"phone":1,"address":1,"remark":1,"fee":1}},{"name":"footer","set":{"fontSize":12,"paytime":1,"ordernum":1,"barcode":1,"needpay":1,"getmoney":1,"paytype":1,"coupon":1,"smallchange":1,"returnmoney":1}},{"name":"text","set":{"text":"谢谢惠顾，欢迎再次光临！","fontSize":20}}],"type":1,"width":180,"paperwidth":58,"setInfo":[{"name":"store","set":{"fontSize":20}},{"name":"header","set":{"fontSize":12,"text":"收银小票","headerfontSize":18,"ordertype":1,"ordernum":1,"ordertime":1,"num":1,"cashier":1}},{"name":"goods","set":{"fontSize":12,"goodsname":1,"price":1,"num":1,"allmoney":1,"sku":1}},{"name":"vip","set":{"fontSize":12,"name":1,"cardnum":1,"money":1,"score":1}},{"name":"takegoods","set":{"fontSize":12,"name":1,"phone":1,"address":1,"remark":1,"fee":1}},{"name":"footer","set":{"fontSize":12,"paytime":1,"ordernum":1,"barcode":1,"needpay":1,"getmoney":1,"paytype":1,"coupon":1,"smallchange":1,"returnmoney":1}},{"name":"text","set":{"text":"谢谢惠顾，欢迎再次光临！","fontSize":20}}]}';
                data = JSON.parse(data);
                _self.printSet = data;
                _self.paperwidth = data.width;
              }
            },
          });
        },

        bindPrint: function () {
          if (!LODOPbol) {
            return;
          }
          var vm = this;
          if (this.printSet.length == 0) {
            Preview1();
          } else {
            // vm.printorderinfo = res.info;
            var str = $(vm.$refs.printorderstr).html();

            Preview2(str);
          }
        },
        //抹零
        returnZero: function (index) {
          let _self = this;
          if (index) {
            _self.cz_shou_qian = String(Number(_self.unChangedCash).toFixed(2));
            _self.cash = Number(_self.unChangedCash) * 100;
            _self.smallChangeMoney = 0;
            _self.isReturnZero = false;
          } else {
            if (_self.unChangedCash == _self.cz_shou_qian) {
              _self.formerMoney = Number(_self.cz_shou_qian);
              _self.formrCash = _self.cash;
            } else {
              _self.formerMoney = Number(_self.unChangedCash);
              _self.formrCash = Number(_self.unChangedCash);
            }
            let nowMoney = _self.formerMoney | 0;
            _self.smallChangeMoney =
              (_self.formerMoney * 100 - nowMoney * 100) | 0;
            _self.cz_shou_qian = String(nowMoney.toFixed(2));
            _self.cash = nowMoney * 100;
            _self.isReturnZero = true;
          }
        },
      },

      filters: {
        // 格式化充值金额/100
        filterMoney: function (money) {
          return (money / 100).toFixed(2);
        },
        //格式化正负号
        formatMark: function (money) {
          if (money.indexOf("-") != -1) {
            money = money.split("-")[1];
          }
          return money;
        },
      },

      computed: {
        kd_shishou: function () {
          if (this.isReturnZero) {
            let useMoney = Number(this.cz_shou_qian);
            let total = useMoney - this.cash / 100;
            if (total > 0) {
              return "找零:" + total.toFixed(2) + "元";
            } else if (total < 0) {
              return "还缺:" + (-total).toFixed(2) + "元";
            } else {
              return "不用找零";
            }
          } else {
            let useMoney = this.cz_shou_qian;
            let total = useMoney - this.cash / 100;
            if (total > 0) {
              return "找零:" + total.toFixed(2) + "元";
            } else if (total < 0) {
              return "还缺:" + (-total).toFixed(2) + "元";
            } else {
              return "不用找零";
            }
          }
        },
        receivableing: function () {
          let money = 0;
          if (this.orderDetails) {
            if (this.orderDetails.dismoney != "0.00") {
              money =
                money + Math.round(Number(this.orderDetails.dismoney) * 100);
            }
            if (this.orderDetails.deduction != "0.00") {
              money =
                money + Math.round(Number(this.orderDetails.deduction) * 100);
            }
            if (this.orderDetails.member_counpon_money) {
              money = money + this.orderDetails.member_counpon_money;
            }
            if (this.orderDetails.manually) {
              money = money + this.orderDetails.manually;
            }
            if (this.orderDetails.small_change_money) {
              money = money + this.orderDetails.small_change_money;
            }
            money =
              money + Math.round(Number(this.orderDetails.receivable) * 100);
            if (this.orderDetails.dispatch_fee != "0.00") {
              money =
                money -
                Math.round(Number(this.orderDetails.dispatch_fee) * 100);
            }
            return money;
          } else {
            return 0;
          }
        },

        // toBePay: function () {
        //     let product = this.orderDetails;
        //     // 所有卡项
        //     let payCardInfo = this.payCardInfo;
        //     // 选择支付充值卡
        //     let offter = this.checkedOffer;
        //     // 支付必须使用卡项
        //     let requisiteCard = this.requisiteCard;
        //
        //     let money = 0;
        //     let offterMoney = 0;
        //     let payPrince = 0;
        //     // let allPay = 0;
        //
        //     // 计算卡的剩余金额
        //     // for (var i = 0; i < product.length; i++) {
        //     //     money += product[i].num * product[i].price - product[i].reduceprice;
        //     //     if (product[i].equity_type == 2 || product[i].equity_type == 3) {
        //     //         for (var j = 0; j < requisiteCard.length; j++) {
        //     //             if (product[i].card_id == requisiteCard[j].cardId) {
        //     //                 requisiteCard[j]['money'] = parseInt(requisiteCard[j].residuebalance - product[i].smallTotal);
        //     //                 offterMoney += requisiteCard[j]['money']
        //     //             }
        //     //         }
        //     //     }
        //     // }
        //     // console.log(`money ${money}`);
        //     // console.log(`offterMoney ${offterMoney}`);
        //     // let pendingPrice = 0;
        //     //
        //     // // allPay = money - offterMoney;
        //     // this.remaining = money - offterMoney;
        //     // console.log('展示的值', this.remaining);
        //     // if (this.remaining < 0) {
        //     //     // console.log(`remaining ${remaining}`);
        //     //     this.remaining = 0;
        //     // } else {
        //     //     for (var i = 0; i < offter.length; i++) {
        //     //         if (offter[i].id == this.selectId) {
        //     //             this.remaining -= offter[i].residuebalance;
        //     //         }
        //     //     }
        //     //
        //     // }
        //
        //     // return this.remaining
        // }
      },

      watch: {
        buyReceipt: {
          handler: function (n) {
            this.cz_qudan = n;
          },
          deep: true,
          immediate: true,
        },
        loginInfo: {
          handler: function (n) {
            this.userInfo = n;
          },
          deep: true,
          immediate: true,
        },
        orderNo: {
          handler: function (n) {
            this.orderNumber = n;
          },
          deep: true,
          immediate: true,
        },
        useCard: {
          handler: function (n) {
            this.isCard = n;
          },
          deep: true,
          immediate: true,
        },
        showMoneys: {
          handler: function (n) {
            this.cz_shou_qian = this.showMoneys;
            this.cash = Number(this.showMoneys) * 100;
            this.unChangedCash = this.showMoneys;
          },
          deep: true,
          immediate: true,
        },
      },
    });
  });
});

// 0 待付款
const orderTable = [];

// 1：品项（产品服务开单），2：品项（产品服务开单）3：购买卡项，4：充值 5：充卡6直接收款
const labelArr = [
  {
    label: "所有类型",
    id: 0,
    tagClass: "",
  },
  {
    label: "服务",
    id: 1,
    tagClass: "o-tag-indigo",
  },
  {
    label: "产品消费",
    id: 2,
    tagClass: "o-tag-indigo",
  },
  {
    // 办卡
    label: "套餐卡",
    id: 3,
    tagClass: "o-tag-fuchsia",
  },
  {
    label: "余额充值",
    id: 4,
    tagClass: "",
  },
  {
    label: "卡项充次",
    id: 5,
    tagClass: "o-tag-lime",
  },
  {
    label: "直接收款",
    id: 6,
    tagClass: "o-tag-pink",
  },
];

const orderDetails = [
  {
    id: 0,
    name: "美白",
    price: "12.5",
    num: 1,
  },
  {
    id: 0,
    name: "美白",
    price: "12.5",
    num: 1,
  },
];

var app = new Vue({
  el: "#order",
  data: {
    istbz: top.app.istbz,
    loadingOrderTable: false,
    loadingRefundList: false,
    loading3: false,
    url: baseUrl,
    //跳到Pay
    billToPay: 0,
    loginInfo: {},
    tabCur: 0,
    orderTable: orderTable,
    labelArr: labelArr,
    typePopover: false,
    curLabel: "",
    typeIndex: 0,
    limit: 10,
    currentPage: 1,
    allCount: 0,
    orderNo: 0,
    buy_receipt: false,
    isRechargeCard: false,

    //详情
    isDetails: false,
    orderDetails: {},
    //打印
    isPrint: false,
    key_num: "",
    paperwidth: 0, //打印纸宽度
    printSet: [], // 设置打印

    //发货
    isdeliverBox: false,
    value: true,
    logisticCompanys: [], //所有物流公司
    companyName: "", //公司名称
    logisticsNum: "", //物流单号
    addressInfo: {}, //地址信息
    orderId: "",

    //自提
    isStoreBox: false,

    //修改员工业绩
    isModifyPerformance: false,
    zuhekaPerformance: 1, //所有业绩提成信息
    totalPerformance: {},
    performanceList: [], //业绩提成信息列表
    salesmenList: [], //销售业绩列表
    techniciansList: [], //服务人员业绩列表
    isSales: false, //选择销售弹框
    isCrafts: false, //选择服务人员弹框
    isHandelIndex: 0, //每条服务在列表中的下标
    AllSales: [], //所有销售和服务人员
    AllCrafts: [], //所有服务人员
    deductType: [
      {
        name: "比例提成",
        id: 1,
      },
      {
        name: "固定提成",
        id: 2,
      },
    ], //提成方式下拉框
    isDeductType: "", //选择的提成方式
    salesChecked: [], //选中的销售存储
    craftsChecked: [], //存储选中的服务人员
    allDelect: [],
    saveModifyArr: [], //存储修改的数据
    addArr: [], //存储增添的数据
    delArr: [], //存储删除的数据
    loginModify: undefined,

    isStaffCollect: false, //服务人员代收
    staffCollectData: {},
    orderInfo: {},

    //address_info,判断是否为空
    addressInfoFlag: true,

    //还款
    isDebt: false,

    leftMenu: [{ word: "订单列表" }, { word: "退款申请" }],
    isActive: 0,

    //退款
    refundList: [], //退款数组
    refundListCount: 0, //退款数组长度
    currentRefundPage: 1,
    refundOptions: [
      { label: "全部状态", value: "" },
      { label: "已通过", value: 1 },
      { label: "未审核", value: 2 },
      { label: "已拒绝", value: 3 },
    ],
    refundOptionsValue: "",
    refundDateRange: "",
    refundKeyNum: "",
    isApply: false,
    refundApplyData: false,

    //退款按钮
    isRefundGoods: false, //退款商品弹框
    isRefundCard: false, //查看退款卡项弹框
    refundCardData: {}, //退款卡项弹框中的数据
    isRefundMoney: false, //主动退款的弹框，终于到退款了
    refundMoneyForm: {
      maxMoney: 0,
      money: 0,
      // refundType:1,
      remark: "",
    },
    deductionArr: [],
    deductionNum: 0,
    amountArr: [],
    refundPayment: [],
    refundPayMode: 0,
    // Refund:[
    //     {id:1,name:'现金退还'},
    //     {id:0,name:'原路返回'}
    // ]
    refundCoupon: 0, //是否退优惠券：0 否 1 是
    repayTotalMoney: 0,
    presentRefund: 1, //默认是否退回
    refundRemarks: "", //订单备注
    isRefundPassword: false, //退款确认密码弹框
    refundPassword: "", //退款确认密码
    refundData: {},
  },

  mounted: function () {
    this.getLoginInfo();
    this.getInit();
    this.getReceiptSet();
    this.handleUrl();
    this.loginModify = global.login;
  },

  methods: {
    getmenu: function (index) {
      this.isActive = index;
      if (this.isActive == 1) {
        this.getRefundList();
      } else {
        this.isFlag = true;
      }
    },

    toCustomerPage(id) {
      let href = "huiyuan.html?id=" + id;
      top.app.toPage(href);
    },

    formatMoney(money) {
      return ff_util.formatMoney(money);
    },

    handleRowClick(row) {
      this.$refs.orderTable.toggleRowExpansion(row);
    },
    // 计算table高度
    updateTableHeight(lenght) {
      const headerHeight = 36 + 1;
      const rowHeight = 36;
      return lenght
        ? headerHeight + rowHeight * lenght
        : headerHeight + rowHeight;
    },

    //处理url
    handleUrl: function () {
      var url = location.search;
      if (url.indexOf("=") != -1) {
        let row = {};
        row.id = url.split("=")[1];
        this.bindSeeDetails(row);
        //解决跳转到预约页面时，header中的选中样式还在会员的问题
        //0:收银台 1:商品 2:订单 3:会员 4:预约 5:通知
        try {
          parent.window.activeHeader(2);
        } catch (e) {}
      }
    },

    //搜索框输入
    handleSearch: function () {
      this.currentPage = 1;
      this.getInit();
    },

    getLoginInfo: function () {
      if (localStorage.getItem("loginInfo")) {
        this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
      } else {
        this.$message({
          type: "error",
          message: "请登录",
          duration: 1500,
        });
      }
    },

    getInit: function () {
      let _self = this;
      _self.loadingOrderTable = true;
      $.ajax({
        url: _self.url + "/android/Orderlist/OrderList",
        type: "POST",
        data: {
          merchantid: _self.loginInfo.merchantid, //  商户id
          limit: _self.limit, // 美业条数
          order_type: _self.curLabel, // 订单类型 1服务 2产品 3售卡 4充值
          page: _self.currentPage, // 页数
          storeid: _self.loginInfo.storeid, // 门店id
          switchs: _self.tabCur, // 状态 0全部 1代付款 2代发货 3已发货 4已完成 5已取消 6代收款 7待核销
          key_num: _self.key_num,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res && res.code == 0) {
            _self.orderTable = res.data;
            _self.allCount = res.count;
          }
        },
        error: function (err) {},
        complete: () => {
          _self.loadingOrderTable = false;
        },
      });
    },

    //获取退款列表接口
    getRefundList: function () {
      let _self = this;
      _self.loadingRefundList = true;
      $.ajax({
        url: _self.url + "/android/Refund/getRefundList",
        type: "POST",
        data: {
          merchantid: _self.loginInfo.merchantid, //  商户id
          storeid: _self.loginInfo.storeid, // 门店id
          starttime: _self.refundDateRange ? _self.refundDateRange[0] : "", // 	开始时间（筛选用，默认空）
          endtime: _self.refundDateRange ? _self.refundDateRange[1] : "", // 		结束时间（筛选，默认空）
          limit: _self.limit, // 每页条数
          page: _self.currentRefundPage, // 页数
          status: _self.refundOptionsValue, // 审核状态：1 审核通过 2 审核中/未审核 3 已拒绝通过 （帅选用，默认空）
          order_number: _self.refundKeyNum,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res && res.code == 0) {
            _self.refundList = res.data;
            _self.refundListCount = res.count;
          }
        },
        error: function (err) {},
        complete: () => {
          _self.loadingRefundList = false;
        },
      });
    },

    bindTabCur: function () {
      this.key_num = "";
      this.currentPage = 1;
      this.getInit();
      this.$nextTick(() => {
        this.$refs.orderTable.bodyWrapper.scrollTop = 0;
      });
    },

    //设置table颜色
    headerClass: function ({ row, rowIndex }) {
      return "background:#f6f6f6;color:#333;fontSize:16px;font-weight:600";
    },

    bindLabel: function (data, index) {
      this.curLabel = data.id;
      this.currentPage = 1;
      this.getInit();
      this.typeIndex = index;
      this.typePopover = false;
    },

    bindIsTypeCancel: function () {
      this.typePopover = false;
    },

    //分页
    handleSizeChange(val) {},
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getInit();
      this.$nextTick(() => {
        this.$refs.orderTable.bodyWrapper.scrollTop = 0;
      });
    },

    //退款列表查询订单号
    handleRefundSearch: function () {
      this.currentRefundPage = 1;
      this.getRefundList();
    },

    //筛选退款列表的状态
    changeRefundOptions: function (val) {
      this.getRefundList();
    },

    //筛选退款列表的申请时间
    cahngeRefundDateRange: function (val) {
      this.getRefundList();
    },

    //退款列表改变页数
    handleRefundCurrentChange: function (val) {
      this.currentRefundPage = val;
      this.getRefundList();
    },

    //查看和审核的退款列表详情
    getRefundDetail: function (row) {
      let _self = this;
      let id = row.id;
      $.ajax({
        url: _self.url + "/android/Refund//getRefundFind",
        type: "POST",
        data: {
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
          id: id,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.refundApplyData = res.data;
            _self.isApply = true;
          }
        },
        error: function (err) {},
      });
    },

    //退款列表查看订单详情
    seeRefundDetail: function (row) {
      this.isDetails = true;
      let id = row.order_id;
      var _self = this;
      $.ajax({
        url: _self.url + "/android/Orderlist/OrderDetails",
        type: "POST",
        data: {
          id: id,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.orderDetails = res.data;

            if (JSON.stringify(res.data.address_info) == "{}") {
              _self.addressInfoFlag = false;
            }
            if (_self.orderDetails.orderInfo.length > 0) {
              let total = 0;
              _self.orderDetails.orderInfo.forEach((item) => {
                let price = Number(item.price) * 100 * item.num;
                total = total + price;
              });
              _self.orderDetails.summation = total;
            }
          }
        },
        error: function (err) {},
      });
    },

    //拒绝退款
    refusedRefund: function () {
      let _self = this;
      $.ajax({
        url: _self.url + "/android/Refund/rejectAudit",
        type: "POST",
        data: {
          merchantid: _self.loginInfo.merchantid, //  商户id
          storeid: _self.loginInfo.storeid, // 门店id
          id: _self.refundApplyData.id, // 申请信息id
          order_id: _self.refundApplyData.order_id, // 订单id
          refuse_reason: _self.refundApplyData.refuse_reason, // 审
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.$message({
              type: "success",
              message: res.msg,
              duration: 1500,
            });
            _self.isApply = false;
            _self.getRefundList();
          }
        },
        error: function (err) {},
      });
    },

    /**
     *  详情
     *
     * */

    bindSeeDetails: function (row) {
      this.loading3 = true;
      this.isDetails = true;
      var id = row.id;
      var _self = this;
      $.ajax({
        url: _self.url + "/android/Orderlist/OrderDetails",
        type: "POST",
        data: {
          id: id,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.orderDetails = res.data;

            if (JSON.stringify(res.data.address_info) == "{}") {
              _self.addressInfoFlag = false;
            }
            if (_self.orderDetails.orderInfo.length > 0) {
              let total = 0;
              _self.orderDetails.orderInfo.forEach((item) => {
                let price = Number(item.price) * 100 * item.num;
                total = total + price;
              });
              _self.orderDetails.summation = total;
            }
          }
        },
        error: function (err) {},
        complete: () => {
          _self.loading3 = false;
        },
      });
    },

    bindCancelDetails: function (obj) {
      this.isDetails = obj;
      //console.log(obj);
    },

    bindPrint: function () {
      console.log("bindPrint", this.orderDetails);
      this.isPrint = true;
    },

    /**小票**/
    bindPrintCancen: function () {
      this.isPrint = false;
    },

    bindPrintConfirm: function () {
      // this.isPrint = false;
      if (!LODOPbol) {
        this.noPrint();
        return;
      }
      var vm = this;
      if (this.printSet.length == 0) {
        Preview1();
      } else {
        // vm.printorderinfo = res.info;
        var str = $(vm.$refs.printorderstr).html();

        Preview2(str);
      }
    },

    // 没有安装打印机
    noPrint: function () {
      let self = this;
      self.$message({
        type: "error",
        message: "打印机未准备好,无法打印",
        duration: 1500,
        onClose: function () {
          LODOPbol = false;
        },
      });
    },

    // 获取小票样式
    getReceiptSet: function () {
      var _self = this;
      // console.log(this.loginInfo);
      $.ajax({
        url: _self.url + "/android/order/getReceiptSet",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.printSet = res.data;
            _self.paperwidth = res.data.width;
          } else {
            let data =
              '{"id":1,"storeid":1,"merchantid":1,"name":"默认小票","status":1,"addtime":"2020-01-01 08:00","set":[{"name":"store","set":{"fontSize":20}},{"name":"header","set":{"fontSize":12,"text":"收银小票","headerfontSize":18,"ordertype":1,"ordernum":1,"ordertime":1,"num":1,"cashier":1}},{"name":"goods","set":{"fontSize":12,"goodsname":1,"price":1,"num":1,"allmoney":1,"sku":1}},{"name":"vip","set":{"fontSize":12,"name":1,"cardnum":1,"money":1,"score":1}},{"name":"takegoods","set":{"fontSize":12,"name":1,"phone":1,"address":1,"remark":1,"fee":1}},{"name":"footer","set":{"fontSize":12,"paytime":1,"ordernum":1,"barcode":1,"needpay":1,"getmoney":1,"paytype":1,"coupon":1,"smallchange":1,"returnmoney":1}},{"name":"text","set":{"text":"谢谢惠顾，欢迎再次光临！","fontSize":20}}],"type":1,"width":180,"paperwidth":58,"setInfo":[{"name":"store","set":{"fontSize":20}},{"name":"header","set":{"fontSize":12,"text":"收银小票","headerfontSize":18,"ordertype":1,"ordernum":1,"ordertime":1,"num":1,"cashier":1}},{"name":"goods","set":{"fontSize":12,"goodsname":1,"price":1,"num":1,"allmoney":1,"sku":1}},{"name":"vip","set":{"fontSize":12,"name":1,"cardnum":1,"money":1,"score":1}},{"name":"takegoods","set":{"fontSize":12,"name":1,"phone":1,"address":1,"remark":1,"fee":1}},{"name":"footer","set":{"fontSize":12,"paytime":1,"ordernum":1,"barcode":1,"needpay":1,"getmoney":1,"paytype":1,"coupon":1,"smallchange":1,"returnmoney":1}},{"name":"text","set":{"text":"谢谢惠顾，欢迎再次光临！","fontSize":20}}]}';
            data = JSON.parse(data);
            _self.printSet = data;
            _self.paperwidth = data.width;
          }
        },
      });
    },

    //还款
    rePayOrder: function () {
      let orderNo = this.orderDetails.order_number;
      this.orderNo = orderNo;
      this.billToPay = 2;
      this.isDebt = true;
      this.buy_receipt = true;
    },

    //主动退款
    activeRefund: function () {
      let _self = this;
      _self.deductionNum = 0;
      _self.deductionArr = [];
      _self.amountArr = [];
      _self.refundPayment = JSON.parse(
        JSON.stringify(_self.orderDetails.payment)
      );
      if (_self.refundPayment.length > 0) {
        _self.refundPayment.forEach((item) => {
          item.refundMode = 0;
          item.repayMoney = item.trade_amount;
        });
      }
      if (_self.orderDetails && _self.orderDetails.orderInfo) {
        for (var i = 0; i < _self.orderDetails.orderInfo.length; i++) {
          let item = _self.orderDetails.orderInfo[i];
          if (item["equity_type"] == 3) {
            _self.deductionArr.push(JSON.parse(JSON.stringify(item)));
            _self.deductionNum += item["num"];
          } else {
            _self.amountArr.push(JSON.parse(JSON.stringify(item)));
          }
        }
      }
      _self.isRefundGoods = true;
    },

    //改变退款方式
    changeRefundType: function (val) {
      this.$forceUpdate();
      // this.refundPayment.forEach(item=>{
      //     console.log(item.refundType);
      // })
    },

    //改变输入框的值
    changeRefundMoney: function (index, val) {
      // console.log(index);
      // console.log(val);
      let money =
        Math.round(Number(val) * 100) <=
        Math.round(Number(this.refundPayment[index].trade_amount) * 100)
          ? val
          : this.refundPayment[index].trade_amount;
      let reg = /(?!0+(?:\.0+)?$)(?:[1-9]\d*|0)(?:\.\d{1,2})?/g;
      if (money == "0.0" || money == "00") {
        if (money == "00") {
          money = "0";
        } else {
          money = "0.";
        }
      } else if (money && money != "0" && money != "0.") {
        let result = money.match(reg);
        money = result[0];
      }

      this.refundPayment[index].repayMoney = money;
      let total = 0;
      this.refundPayment.forEach((item) => {
        // console.log(item.repayMoney)
        total = total + Math.round(Number(item.repayMoney) * 100);
      });
      this.repayTotalMoney = total;
      this.$forceUpdate();
    },

    focusMoney: function () {},

    //主动退款-下一步
    nextRefund: function () {
      this.isRefundMoney = true;
    },

    cancelRefundMoney: function () {
      this.isRefundMoney = false;
      this.isRefundGoods = false;
      this.isRefundCard = false;
      this.refundRemarks = "";
    },

    cancelComfirmRefund: function () {
      this.isRefundPassword = false;
      this.refundData = {};
      this.refundPassword = "";
    },

    //查看退款卡项详情
    activeRefundCard: function () {
      let _self = this;
      $.ajax({
        url: _self.url + "/android/Orderlist/getVipCardData",
        type: "POST",
        data: {
          merchantid: _self.loginInfo.merchantid, //商户id
          storeid: _self.loginInfo.storeid, //门店id
          memberId: _self.orderDetails.vip_id, //会员id
          order_id: _self.orderDetails.id, //订单id
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.refundCardData = res.data;

            _self.isRefundCard = true;
          }
        },
        error: function (err) {
          consle.log(err);
        },
      });
    },

    //退款
    checkRefund: function () {
      let _self = this;
      let refundData1 = {};
      refundData1["cashier_id"] = _self.loginInfo.id;
      refundData1["merchantid"] = _self.loginInfo.merchantid;
      refundData1["storeid"] = _self.loginInfo.storeid;
      refundData1["shift_no"] = _self.loginInfo.shift_no;
      refundData1["orderNo"] = _self.orderDetails.order_number;
      refundData1["orderType"] = 1;
      refundData1["presentRefund"] = _self.presentRefund;
      refundData1["presentRefund"] = _self.presentRefund;
      refundData1["refundCoupon"] = _self.refundCoupon;
      refundData1["remarks"] = _self.refundRemarks;
      let refunds = [];
      _self.refundPayment.forEach((item) => {
        refunds.push({
          amount: Math.round(Number(item.repayMoney) * 100),
          orderNo: _self.orderDetails.order_number,
          payNo: item.out_trade_no,
          payType: item.payType,
          refundMode: item.refundMode,
        });
      });
      refundData1["refunds"] = JSON.stringify(refunds);
      _self.refundData = refundData1;

      _self.isRefundPassword = true;
      _self.$nextTick(() => {
        _self.$refs.refundMoneyPass.focus();
      });
    },

    //确认退款
    comfirmRefund: function () {
      let _self = this;

      if (_self.refundPassword) {
        _self.refundData.password = _self.refundPassword;
        $.ajax({
          url: _self.url + "/android/Orderlist/orderRefund",
          type: "POST",
          data: _self.refundData,
          success: function (res) {
            // var res = JSON.parse(res);
            if (res.code == 1) {
              _self.$message({
                type: "success",
                message: res.msg,
                duration: 1500,
              });
              _self.isRefundPassword = false;
              _self.refundPassword = "";
              _self.cancelRefundMoney();
              _self.bindSeeDetails(_self.orderDetails);
              _self.getInit();
              _self.refundData = JSON.parse(JSON.stringify({}));
            } else {
              _self.$message({
                type: "error",
                message: res.msg,
                duration: 1500,
              });
            }
          },
          error: function (err) {},
        });
      } else {
        _self.$message({
          type: "warning",
          message: "密码不能为空",
          duration: 1500,
        });
      }
    },

    // 取消订单
    bindCancelOrder: function () {
      var _self = this;
      this.$confirm("是否取消订单?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          _self.cancelOrder();
        })
        .catch(() => {});
    },

    //直接收款
    bindDirectPay: function () {
      let orderNo = this.orderDetails.order_number;
      this.orderNo = orderNo;
      this.billToPay = 2;
      this.buy_receipt = true;
    },

    //关闭收款
    bindClosePay: function (flag) {
      this.bindSeeDetails(this.orderDetails);
      this.buy_receipt = flag;
    },

    //修改订单
    bindModifyOrder: function () {
      var _self = this;
      _self.loading = true;
      var orderNo = _self.orderDetails.order_number;
      var vip_id = _self.orderDetails.vip_id;
      // console.log('会员id', vip_id);
      // console.log('订单号', orderNo);
      _self.loading = true;
      // window.location.href = "cashier_system.html?orderNO="+orderNo+'&vip_id=' + vip_id;
      //window.location.href = "cashier_system.html?orderNO=" + orderNo + '&vip_id=' + vip_id;
      let href = "cashier_system.html?orderNO=" + orderNo + "&vip_id=" + vip_id;
      top.app.toPage(href);
      // localStorage.setItem('headerAtive', 0);
      // if (vip_id==0){
      //     window.location.href = "cashier_system.html?orderNO="+orderNo;
      // }else{
      //
      // }
      // location.href = "cashier_system.html"
      // window.location.href = "cashier_system.html?orderNO="+orderNo+'&vip_id=' + vip_id;
    },

    cancelOrder: function () {
      var _self = this;
      var vip_id = _self.orderDetails.vip_id;
      $.ajax({
        url: _self.url + "/api/Orderlist/newCancelOrder",
        type: "post",
        data: {
          id: _self.orderDetails.id,
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
          vipId: vip_id,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.isDetails = false;
            _self.currentPage = 1;
            _self.getInit();
          } else {
            _self.$message({
              type: "error",
              message: res.msg,
              duration: 1500,
            });
          }
        },
        error: function (res) {},
      });
    },
    //自提
    StoreGoods: function () {
      var _self = this;
      this.isStoreBox = true;
      _self.addressInfo = _self.orderDetails.address_info;
    },
    //获取所有物流公司、发货
    deliverGoods: function () {
      var _self = this;
      this.isdeliverBox = true;
      _self.addressInfo = _self.orderDetails.address_info;
      $.ajax({
        url: _self.url + "/android/Orderlist/getLogistics",
        type: "post",
        success: function (res) {
          // var res = JSON.parse(res);
          _self.logisticCompanys = res.data;
        },
        error: function (e) {},
      });
    },
    chooseCompany: function (value) {
      var _self = this;
      _self.orderId = value;
    },
    //确认发货方法
    sendGoods: function () {
      var _self = this,
        delivery = 1;

      if (_self.value) {
        delivery = 1;
      } else {
        delivery = 2;
      }
      console.log(
        this.logisticsNum,
        this.orderId,
        this.orderDetails.dispatch_type,
        this.orderDetails.id,
        delivery
      );
      $.ajax({
        url: _self.url + "/android/Orderlist/deliverGoods",
        type: "post",
        data: {
          courier_num: _self.logisticsNum,
          delivery: delivery,
          dispatch_type: _self.orderDetails.dispatch_type,
          logistics: this.orderId,
          orderid: this.orderDetails.id,
        },
        success: function (res) {
          // var res = JSON.parse(res);

          _self.$message({
            type: "success",
            message: res.msg,
            duration: 1500,
          });
          _self.tabCur = 2;
          _self.bindTabCur();
          _self.isdeliverBox = false;
          _self.isStoreBox = false;
          _self.isDetails = false;
        },
        error: function (e) {},
      });
    },
    //开关控制物流信息
    changeStatus: function () {
      var _self = this;
      if (_self.value == false) {
        //清空输入信息
        _self.logisticsNum = "";
        _self.companyName = "";
        _self.orderId = "";
      }
    },
    //发货弹框关闭执行方法
    closeReceiptBox: function () {
      //清空输入信息
      var _self = this;
      _self.logisticsNum = "";
      _self.companyName = "";
      _self.value = true;
      _self.orderId = "";
    },

    //获取所有销售和服务人员
    getAllSales: function () {
      var _self = this;
      $.ajax({
        url: _self.url + "/android/Deduct/getStaff",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.loading = false;
            _self.AllSales = res.data;
          } else {
            _self.$message({
              message: res.msg,
              type: "warning",
              duration: 1500,
            });
            _self.loading = false;
          }
        },
        error: function (error) {},
      });
    },

    //获取单据业绩提成记录
    modifyEmployeePerformance: function () {
      var _self = this;
      this.isModifyPerformance = true;
      // console.log(_self.loginInfo.merchantid,_self.loginInfo.storeid,_self.kd_xinxi_list.order_number)
      this.loading = true;
      $.ajax({
        url: _self.url + "/android/Deduct/getOrderDeductData",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid,
          orderNo: _self.orderDetails.order_number,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.loading = false;
            _self.zuhekaPerformance = res.data.type;
            _self.totalPerformance = res.data;
            _self.performanceList = res.data.performance;
            // console.log(_self.performanceList[0].salesmen)
            let length = _self.performanceList.length - 1;
            for (let i = 0; i < _self.performanceList.length; i++) {
              _self.performanceList[i].salesChecked = [];
              _self.performanceList[i].craftsChecked = [];
              if (_self.performanceList.base_amount) {
              } else {
                _self.performanceList[i].base_amount =
                  _self.performanceList[length].base_amount;
              }
            }
          } else {
            _self.$message({
              message: res.msg,
              type: "warning",
              duration: 1500,
            });
            _self.loading = false;
          }
        },
        error: function (error) {},
      });
      _self.getAllSales();
    },

    //选择销售
    chooseSales: function (performanceList, index) {
      var _self = this;
      this.isSales = true;
      this.isHandelIndex = index;
      _self.salesChecked =
        _self.performanceList[_self.isHandelIndex].salesChecked;
      for (let j = 0; j < _self.AllSales.length; j++) {
        Vue.delete(_self.AllSales[j], "isDisabled");
        for (
          let i = 0;
          i < _self.performanceList[_self.isHandelIndex].salesmen.length;
          i++
        ) {
          if (
            _self.performanceList[_self.isHandelIndex].salesmen[i].staff_id ==
            _self.AllSales[j].id
          ) {
            _self.AllSales[j].isDisabled = 1;
          }
        }
      }
    },
    //添加销售
    addSalesmen: function () {
      var _self = this;
      this.isSales = false;
      let salesmenLength =
        _self.performanceList[_self.isHandelIndex].salesmen.length;
      // console.log(_self.salesChecked,"下标")
      _self.performanceList[_self.isHandelIndex].addSalesmen = []; //存储添加的销售
      _self.performanceList[_self.isHandelIndex].salesChecked =
        _self.salesChecked;
      for (let i = 0; i < _self.salesChecked.length; i++) {
        salesmenLength += 1;
        _self.performanceList[_self.isHandelIndex].addSalesmen[i] = {
          staffName: _self.AllSales[_self.salesChecked[i]].nickname,
          lengthh: salesmenLength,
          assign: 2,
          base_amount: _self.performanceList[_self.isHandelIndex].base_amount,
          commission: 0.0,
          commission_proportion: 0.0,
          performance: 0.0,
          performance_proportion: 0.0,
          deduct_type: 1,
          deduct_way: 1,
          order_time: _self.totalPerformance.order_time,
          staff_id: _self.AllSales[_self.salesChecked[i]].id,
          storeid: _self.totalPerformance.storeid,
          merchantid: _self.totalPerformance.merchantid,
          id: 0,
          order_id: _self.performanceList[_self.isHandelIndex].order_id,
          order_detail_id: _self.performanceList[_self.isHandelIndex].id,
          goods_type:
            _self.zuhekaPerformance == 5 ? 2 : _self.zuhekaPerformance,
        };
      }
    },

    //选择服务人员
    chooseCrafts: function (performanceList, index) {
      var _self = this;
      this.isCrafts = true;
      this.isHandelIndex = index;
      _self.AllCrafts = _self.AllSales.filter(function (items, index, ar) {
        if (items.isTech == 2) {
          return items;
        }
      });
      _self.craftsChecked =
        _self.performanceList[_self.isHandelIndex].craftsChecked;
      for (let j = 0; j < _self.AllCrafts.length; j++) {
        Vue.delete(_self.AllCrafts[j], "isDisabled");
        for (
          let i = 0;
          i < _self.performanceList[_self.isHandelIndex].technicians.length;
          i++
        ) {
          if (
            _self.performanceList[_self.isHandelIndex].technicians[i]
              .staff_id == _self.AllCrafts[j].id
          ) {
            _self.AllCrafts[j].isDisabled = 1;
          }
        }
      }
    },
    //添加服务人员
    addCrafts: function () {
      var _self = this;
      this.isCrafts = false;
      let craftsLength =
        _self.performanceList[_self.isHandelIndex].technicians.length;
      _self.performanceList[_self.isHandelIndex].addCrafts = [];
      _self.performanceList[_self.isHandelIndex].craftsChecked =
        _self.craftsChecked;
      for (let i = 0; i < _self.craftsChecked.length; i++) {
        craftsLength += 1;
        _self.performanceList[_self.isHandelIndex].addCrafts[i] = {
          staffName: _self.AllCrafts[_self.craftsChecked[i]].nickname,
          lengthh: craftsLength,
          assign: 2,
          base_amount: _self.performanceList[_self.isHandelIndex].base_amount,
          commission: 0.0,
          commission_proportion: 0.0,
          performance: 0.0,
          performance_proportion: 0.0,
          deduct_type: 2,
          deduct_way: 1,
          order_time: _self.totalPerformance.order_time,
          staff_id: _self.AllCrafts[_self.craftsChecked[i]].id,
          storeid: _self.totalPerformance.storeid,
          merchantid: _self.totalPerformance.merchantid,
          id: 0,
          order_id: _self.performanceList[_self.isHandelIndex].order_id,
          order_detail_id: _self.performanceList[_self.isHandelIndex].id,
          goods_type:
            _self.zuhekaPerformance == 5 ? 2 : _self.zuhekaPerformance,
        };
      }
    },

    //选择提成方式
    chooseDeductType: function (e, sindex, lindex) {
      this.$forceUpdate();
    },

    limitInput: function (e) {
      // e.target.value = e.target.value.replace(/[^\d\.]/g, '')
      // e.target.value = e.target.value.replace(/^\./g, '0.');
      // e.target.value = e.target.value.replace(/\.{2,}/g, '.');
      // e.target.value = e.target.value.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.');
      // e.target.value = e.target.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
      // e.target.value = e.target.value.replace(/^0.$/, '0.');
    },
    //输入金额呈现百分比
    limitInputMoney: function (e) {
      //e.target._value 当前操作值
      //$(e.path[2]).find('input')[1].value  改变值
      //parseInt(e.path[7].children[0].childNodes[0].textContent)-1  每一项服务在服务数组中下标
      //parseInt(e.path[3].children[0].childNodes[0].textContent)-1 销售或服务人员数组中每一条数据的下标
      let performanceIndex =
        parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
      let lineIndex =
        parseInt(e.path[3].children[0].childNodes[0].textContent) - 1;
      let per = (
        (e.target.value * 10000) /
        this.performanceList[performanceIndex].base_amount
      ).toFixed(2);
      $(e.path[2]).find("input")[1].value = per;
      if (e.path[5].children[0].textContent == "选择销售") {
        this.performanceList[performanceIndex].salesmen[
          lineIndex
        ].performance_proportion = per.toString();
      } else {
        this.performanceList[performanceIndex].technicians[
          lineIndex
        ].performance_proportion = per.toString();
      }
    },
    limitInputMoneyAdd: function (e, items, index) {
      let performanceIndex =
        parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
      $(e.path[2]).find("input")[1].value = (
        (parseInt(items.performance) * 10000) /
        items.base_amount
      ).toFixed(2);
      if (e.path[5].children[0].textContent == "选择销售") {
        this.performanceList[performanceIndex].addSalesmen[
          index
        ].performance_proportion = (
          (parseInt(items.performance) * 10000) /
          items.base_amount
        ).toFixed(2);
      } else {
        this.performanceList[performanceIndex].addCrafts[
          index
        ].performance_proportion = (
          (parseInt(items.performance) * 10000) /
          items.base_amount
        ).toFixed(2);
      }
      this.$forceUpdate();
    },
    limitInputMoneyAdd1: function (e, items, index) {
      let performanceIndex =
        parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
      $(e.path[2]).find("input")[1].value = (
        (parseInt(items.commission) * 10000) /
        items.base_amount
      ).toFixed(2);
      if (e.path[5].children[0].textContent == "选择销售") {
        this.performanceList[performanceIndex].addSalesmen[
          index
        ].commission_proportion = (
          (parseInt(items.commission) * 10000) /
          items.base_amount
        ).toFixed(2);
      } else {
        this.performanceList[performanceIndex].addCrafts[
          index
        ].commission_proportion = (
          (parseInt(items.commission) * 10000) /
          items.base_amount
        ).toFixed(2);
      }
      this.$forceUpdate();
    },
    limitInputMoney1: function (e) {
      let performanceIndex =
        parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
      let lineIndex =
        parseInt(e.path[3].children[0].childNodes[0].textContent) - 1;
      let per = (
        (e.target.value * 10000) /
        this.performanceList[performanceIndex].base_amount
      ).toFixed(2);
      $(e.path[2]).find("input")[1].value = per;
      if (e.path[5].children[0].textContent == "选择销售") {
        this.performanceList[performanceIndex].salesmen[
          lineIndex
        ].commission_proportion = per.toString();
      } else {
        this.performanceList[performanceIndex].technicians[
          lineIndex
        ].commission_proportion = per.toString();
      }
    },
    //输入百分比呈现金额
    limitInputPer: function (e) {
      //e.target._value 当前操作值
      //e.path[2]).find('input')[0].value 改变值
      let performanceMoney =
        parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
      let lineIndex =
        parseInt(e.path[3].children[0].childNodes[0].textContent) - 1;
      let money = (
        (e.target.value * this.performanceList[performanceMoney].base_amount) /
        10000
      ).toFixed(2);
      $(e.path[2]).find("input")[0].value = money;
      if (e.path[5].children[0].textContent == "选择销售") {
        this.performanceList[performanceMoney].salesmen[lineIndex].performance =
          money.toString();
      } else {
        this.performanceList[performanceMoney].technicians[
          lineIndex
        ].performance = money.toString();
      }
    },
    limitInputPer1: function (e) {
      let performanceMoney =
        parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
      let lineIndex =
        parseInt(e.path[3].children[0].childNodes[0].textContent) - 1;
      let money = (
        (Number(e.target.value) *
          this.performanceList[performanceMoney].base_amount) /
        10000
      ).toFixed(2);
      $(e.path[2]).find("input")[0].value = money;
      if (e.path[5].children[0].textContent == "选择销售") {
        this.performanceList[performanceMoney].salesmen[lineIndex].commission =
          money.toString();
      } else {
        this.performanceList[performanceMoney].technicians[
          lineIndex
        ].commission = money.toString();
      }
    },
    limitInputPerAdd: function (e, items, index) {
      let performanceMoney =
        parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
      $(e.path[2]).find("input")[0].value = (
        (parseInt(items.performance_proportion) * items.base_amount) /
        10000
      ).toFixed(2);
      if (e.path[5].children[0].textContent == "选择销售") {
        this.performanceList[performanceMoney].addSalesmen[index].performance =
          (
            (parseInt(items.performance_proportion) * items.base_amount) /
            10000
          ).toFixed(2);
      } else {
        this.performanceList[performanceMoney].addCrafts[index].performance = (
          (parseInt(items.performance_proportion) * items.base_amount) /
          10000
        ).toFixed(2);
      }
      this.$forceUpdate();
    },
    limitInputPerAdd1: function (e, items, index) {
      let performanceMoney =
        parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
      $(e.path[2]).find("input")[0].value = (
        (parseInt(items.commission_proportion) * items.base_amount) /
        10000
      ).toFixed(2);
      if (e.path[5].children[0].textContent == "选择销售") {
        this.performanceList[performanceMoney].addSalesmen[index].commission = (
          (parseInt(items.commission_proportion) * items.base_amount) /
          10000
        ).toFixed(2);
      } else {
        this.performanceList[performanceMoney].addCrafts[index].commission = (
          (parseInt(items.commission_proportion) * items.base_amount) /
          10000
        ).toFixed(2);
      }
      this.$forceUpdate();
    },

    //删除销售、服务人员
    delectsalesmen: function (info, index, inde) {
      var _self = this;
      _self.allDelect.push(_self.performanceList[inde].salesmen[index]);
      _self.performanceList[inde].salesmen.splice(index, 1);
      this.$forceUpdate();
    },
    delectCrafts: function (info, index, inde) {
      var _self = this;
      _self.allDelect.push(_self.performanceList[inde].technicians[index]);
      _self.performanceList[inde].technicians.splice(index, 1);
      this.$forceUpdate();
    },

    //提交保存修改的数据
    saveModify: function () {
      var _self = this;
      _self.loading = true;
      _self.saveModifyArr = [];
      _self.delArr = [];
      _self.addArr = [];
      //遍历拼接数组
      for (let i = 0; i < _self.performanceList.length; i++) {
        _self.saveModifyArr = _self.saveModifyArr.concat(
          _self.performanceList[i].salesmen,
          _self.performanceList[i].technicians
        );
        if (_self.performanceList[i].addSalesmen) {
          if (_self.performanceList[i].addCrafts) {
            _self.addArr = _self.addArr.concat(
              _self.performanceList[i].addSalesmen,
              _self.performanceList[i].addCrafts
            );
          } else {
            _self.addArr = _self.addArr.concat(
              _self.performanceList[i].addSalesmen
            );
          }
        } else {
          if (_self.performanceList[i].addCrafts) {
            _self.addArr = _self.addArr.concat(
              _self.performanceList[i].addCrafts
            );
          } else {
            _self.addArr = _self.addArr;
          }
        }
      }
      //删除多余的属性
      for (let i = 0; i < _self.addArr.length; i++) {
        Vue.delete(_self.addArr[i], "lengthh");
      }
      _self.delArr = _self.allDelect;
      _self.saveModifyArr = JSON.stringify(_self.saveModifyArr);
      _self.delArr = JSON.stringify(_self.delArr);
      _self.addArr = JSON.stringify(_self.addArr);
      $.ajax({
        url: _self.url + "/android/Deduct/saveDeductData",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid,
          orderNo: _self.orderDetails.order_number,
          storeid: _self.loginInfo.storeid,
          addArr: _self.addArr,
          delArr: _self.delArr,
          saveArr: _self.saveModifyArr,
          nickname: _self.loginInfo.nickname,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.loading = false;
            _self.$message({
              message: res.msg,
              type: "success",
              duration: 1500,
            });
            _self.isModifyPerformance = false;
          } else {
            _self.$message({
              message: res.msg,
              type: "warning",
              duration: 1500,
            });
            _self.loading = false;
          }
        },
        error: function (error) {},
      });

      // console.log(_self.addArr)
    },

    //服务人员代收弹框
    staffCollection: function (id, name, orderInfo) {
      let _self = this;

      _self.orderInfo = orderInfo;

      $.ajax({
        url: _self.url + "/android/Orderlist/getOrderPaymentData",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid,
          orderId: id,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            if (res.data.length == 0) {
              _self.staffCollectData = {};
              _self.staffCollectData.staffName = name;
              _self.staffCollectData.trade_amount = orderInfo.receivable;
              _self.isStaffCollect = true;
            } else {
              _self.staffCollectData = res.data[0];
              _self.staffCollectData.staffName = name;
              _self.staffCollectData.trade_amount =
                Number(_self.staffCollectData.trade_amount) / 100;

              _self.isStaffCollect = true;
            }
          } else {
            _self.$message({
              message: res.msg,
              type: "warning",
              duration: 1500,
            });
          }
        },
        error: function (error) {},
      });
    },
    //收银员确认收到钱
    confirmReceipt: function () {
      var _self = this;
      $.ajax({
        url: _self.url + "/android/Orderlist/confirmReceipt",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid,
          orderId: _self.orderInfo.id,
          storeid: _self.loginInfo.storeid,
          cashierId: _self.orderInfo.cashier_id,
          shiftNo: _self.orderInfo.shift_no,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.tabCur = 6;
            _self.bindTabCur();
            _self.isStaffCollect = false;
            _self.$message({
              message: res.msg,
              type: "success",
              duration: 1500,
            });
          } else {
            _self.$message({
              message: res.msg,
              type: "warning",
              duration: 1500,
            });
          }
        },
        error: function (error) {},
      });
    },

    //取消服务人员代收
    cancelStaffCollect() {
      let _self = this;
      _self.isStaffCollect = false;
    },

    //服务人员代收
    saveStaffCollect: function () {
      let _self = this;
      _self.isStaffCollect = false;
    },
    handleRowClick(row) {
      this.$refs.orderTable.toggleRowExpansion(row);
    },
  },

  filters: {
    // 格式化充值金额/100
    filterMoney: function (money) {
      return (money / 100).toFixed(2);
    },
    //格式化正负号
    formatMark: function (money) {
      if (money.indexOf("-") != -1) {
        money = money.split("-")[1];
      }
      return money;
    },
  },
  computed: {},
  watch: {
    key_num: function (n, o) {
      if (!n && o) {
        this.currentPage = 1;
        this.getInit();
      }
    },
    refundPayment: {
      handler: function (n) {
        let total = 0;
        n.forEach((item) => {
          // console.log(item.repayMoney)
          total = total + Math.round(Number(item.repayMoney) * 100);
        });
        this.repayTotalMoney = total;
      },
      deep: true,
    },
  },
});
