const ff_util = {
  formatMoney: (money) => {
    const num = Number(money);
    const isNegative = num < 0;
    const absNum = Math.abs(num);
    const formatted = absNum.toLocaleString("en-US", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
    return (isNegative ? "-￥ " : "￥ ") + formatted;
  },

  // 格式化日期
  formatEleMentPickDate(date) {
    if (!date) return "";
    const d = new Date(date);
    const month = (d.getMonth() + 1).toString().padStart(2, "0");
    const day = d.getDate().toString().padStart(2, "0");
    return `${d.getFullYear()}-${month}-${day}`;
  },

  // 计算从当前时间到指定日期的时间差
  fromTheCurrentTime(date) {
    if (!date) return "--";
    try {
      const today = new Date();
      const inputDate = new Date(date);

      if (isNaN(inputDate.getTime())) return "--";

      let years = today.getFullYear() - inputDate.getFullYear();
      let months = today.getMonth() - inputDate.getMonth();

      if (months < 0) {
        years--;
        months += 12;
      }

      // 如果日期小于当前日期，月份减1
      if (today.getDate() < inputDate.getDate()) {
        months--;
        if (months < 0) {
          years--;
          months += 12;
        }
      }

      // 计算总月份
      const totalMonths = years * 12 + months;

      // 如果小于1年，只返回月份
      if (years < 1) {
        return `${totalMonths} 个月`;
      }

      // 如果小于2年，返回"几年几个月"
      if (years < 2) {
        return `${years} 年 ${months} 个月`;
      }

      // 否则只返回"几年"
      return `${years} 年`;
    } catch (error) {
      return "--";
    }
  },

  // 计算年龄
  getAge(birthday) {
    if (!birthday) return "--";
    try {
      const today = new Date();
      const birthDate = new Date(birthday);

      // 检查birthDate是否为有效日期
      if (isNaN(birthDate.getTime())) return "--";

      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      if (
        monthDiff < 0 ||
        (monthDiff === 0 && today.getDate() < birthDate.getDate())
      ) {
        age--;
      }
      return age + " 岁";
    } catch (e) {
      return "--";
    }
  },

  // 订单类型相关
  OrderType: {
    // 用于下拉框
    labelOptions: [
      {
        label: "所有类型",
        id: 0,
        tagClass: "",
      },
      {
        label: "服务消费",
        id: 1,
        tagClass: "o-tag-indigo",
      },
      {
        label: "产品购买",
        id: 2,
        tagClass: "o-tag-amber",
      },
      {
        // 办卡
        label: "套餐卡购买",
        id: 3,
        tagClass: "o-tag-pink",
      },
      {
        label: "余额充值",
        id: 4,
        tagClass: "o-tag-orange",
      },
      {
        label: "服务充次",
        id: 5,
        tagClass: "o-tag-lime",
      },
      {
        label: "直接收款",
        id: 6,
        tagClass: "o-tag-fuchsia",
      },
    ],
    // 类型名
    getOrderTypeName: function(id) {
      return this.labelOptions.find((item) => item.id == id)?.label || "";
    },
    // 类型颜色css
    getOrderTypeColor: function(id) {
      return this.labelOptions.find((item) => item.id == id)?.tagClass || "";
    },
  },
};
