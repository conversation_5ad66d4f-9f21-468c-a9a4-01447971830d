<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />
    <link rel="stylesheet" href="vue/element/<EMAIL>" />
    <link rel="stylesheet" href="./css/css-comment.css" />
    <link rel="stylesheet" href="css/setting.css" />
    <link
      rel="stylesheet"
      href="http://at.alicdn.com/t/font_1156348_if4g6jkesri.css"
    />
  </head>
  <body>
    <div class="main" id="setting" v-cloak>
      <div class="content">
        <div class="savePay">
          <h3>保存和收款</h3>
          <div class="savePayContent">
            <div>
              <div>开单保存（或收款）是否提示打印耗材</div>
              <el-switch
                v-model="printCostMaterial"
                @change="savePayPrint"
                active-text="是"
                inactive-text="否"
              ></el-switch>
            </div>
            <div>
              <div>关闭提示打印耗材时，是否默认直接打印耗材</div>
              <el-switch
                v-model="defalutPrintCostMaterial"
                :disabled="printCostMaterial"
                @change="savePayPrint1"
                active-text="是"
                inactive-text="否"
              ></el-switch>
            </div>
          </div>
        </div>
      </div>
      <div class="content">
        <div class="savePay">
          <h3>开单加载设置</h3>
          <div class="savePayContent">
            <div>
              <div>一次加载的服务数量</div>
              <el-input-number
                size="small"
                v-model="goodsSearchConfig.serverLimit"
                :precision="0"
                :step="1"
                :max="100"
                :min="10"
              ></el-input-number>
            </div>
            <div>
              <div>一次加载的产品数量</div>
              <el-input-number
                size="small"
                v-model="goodsSearchConfig.productLimit"
                :precision="0"
                :step="1"
                :max="100"
                :min="10"
              ></el-input-number>
            </div>
            <div>
              <div>一次加载的卡项数量</div>
              <el-input-number
                size="small"
                v-model="goodsSearchConfig.cardLimit"
                :precision="0"
                :step="1"
                :max="100"
                :min="10"
              ></el-input-number>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
  <script src="vue/vue2.5.16.js"></script>
  <script src="vue/element/<EMAIL>"></script>
  <script src="js/plugin/<EMAIL>"></script>
  <script>
    new Vue({
      el: "#setting",
      data() {
        return {
          defalutPrintCostMaterial: true,
          printCostMaterial: true,
          goodsSearchConfig: {
            cardLimit: 10,
            productLimit: 10,
            serverLimit: 10,
          },
        };
      },
      mounted() {
        this.changeValue();
      },
      methods: {
        savePayPrint: function (value) {

          localStorage.setItem(
            "printCostMaterial",
            JSON.stringify({ flag: value })
          );
        },
        savePayPrint1: function (value) {

          localStorage.setItem(
            "defalutPrintCostMaterial",
            JSON.stringify({ flag: value })
          );
        },
        changeValue: function () {
          let item = localStorage.getItem("printCostMaterial");
          if (item) {
            item = JSON.parse(item);

            this.printCostMaterial = item.flag;
          } else {
            this.printCostMaterial = true;
          }
          let defaultitem = localStorage.getItem("defalutPrintCostMaterial");
          if (defaultitem) {
            defaultitem = JSON.parse(defaultitem);

            this.defalutPrintCostMaterial = defaultitem.flag;
          } else {
            this.defalutPrintCostMaterial = true;
          }
          let goodsSearchConfig = localStorage.getItem("goodsSearchConfig");
          if (goodsSearchConfig) {
            this.goodsSearchConfig = JSON.parse(goodsSearchConfig);
          }
        },
      },
      watch: {
        goodsSearchConfig: {
          deep: true,
          handler(n) {
            localStorage.setItem("goodsSearchConfig", JSON.stringify(n));
          },
        },
      },
    });
  </script>
</html>
