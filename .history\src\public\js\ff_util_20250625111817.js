const ff_util = {
  formatMoney: (money) => {
    const num = Number(money);
    const isNegative = num < 0;
    const absNum = Math.abs(num);
    const formatted = absNum.toLocaleString("en-US", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
    return (isNegative ? "-￥ " : "￥ ") + formatted;
  },

  OrderType: {
    labelOptions: [
      {
        label: "所有类型",
        id: 0,
        tagClass: "",
      },
      {
        label: "服务消费",
        id: 1,
        tagClass: "o-tag-indigo",
      },
      {
        label: "产品购买",
        id: 2,
        tagClass: "o-tag-amber",
      },
      {
        // 办卡
        label: "套餐卡购买",
        id: 3,
        tagClass: "o-tag-pink",
      },
      {
        label: "余额充值",
        id: 4,
        tagClass: "o-tag-orange",
      },
      {
        label: "服务充次",
        id: 5,
        tagClass: "o-tag-lime",
      },
      {
        label: "直接收款",
        id: 6,
        tagClass: "o-tag-fuchsia",
      },
    ],
  },
};
